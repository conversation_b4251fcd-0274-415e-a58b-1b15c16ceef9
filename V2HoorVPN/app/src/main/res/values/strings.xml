<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">V2Hoor VPN</string>
    <string name="app_widget_name">Switch</string>
    <string name="app_tile_name">Switch</string>
    <string name="app_tile_first_use">First use of this feature, please use the app to add server</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="migration_success">Data migration success!</string>
    <string name="migration_fail">Data migration failed!</string>
    <string name="pull_down_to_refresh">Please pull down to refresh!</string>

    <!-- Notifications -->
    <string name="notification_action_stop_v2ray">Stop</string>
    <string name="toast_permission_denied">Unable to obtain the permission</string>
    <string name="toast_permission_denied_notification">Unable to obtain the notification permission</string>
    <string name="notification_action_more">Click for more</string>
    <string name="toast_services_start">Start Services</string>
    <string name="toast_services_stop">Stop Services</string>
    <string name="toast_services_success">Start Services Success</string>
    <string name="toast_services_failure">Start Services Failure</string>

    <!--ServerActivity-->
    <string name="title_server">Configuration file</string>
    <string name="menu_item_add_config">Add config</string>
    <string name="menu_item_save_config">Save config</string>
    <string name="menu_item_del_config">Delete config</string>
    <string name="menu_item_import_config_qrcode">Import config from QRcode</string>
    <string name="menu_item_import_config_clipboard">Import config from Clipboard</string>
    <string name="menu_item_import_config_local">Import config from locally</string>
    <string name="menu_item_import_config_manually_vmess">Type manually[VMess]</string>
    <string name="menu_item_import_config_manually_vless">Type manually[VLESS]</string>
    <string name="menu_item_import_config_manually_ss">Type manually[Shadowsocks]</string>
    <string name="menu_item_import_config_manually_socks">Type manually[SOCKS]</string>
    <string name="menu_item_import_config_manually_http">Type manually[HTTP]</string>
    <string name="menu_item_import_config_manually_trojan">Type manually[Trojan]</string>
    <string name="menu_item_import_config_manually_wireguard">Type manually[Wireguard]</string>
    <string name="menu_item_import_config_manually_hysteria2">Type manually[Hysteria2]</string>
    <string name="del_config_comfirm">Confirm delete ?</string>
    <string name="del_invalid_config_comfirm">Please test before deleting! Confirm delete ?</string>
    <string name="server_lab_remarks">remarks</string>
    <string name="server_lab_address">address</string>
    <string name="server_lab_port">port</string>
    <string name="server_lab_id">id</string>
    <string name="server_lab_alterid">alterId</string>
    <string name="server_lab_security">security</string>
    <string name="server_lab_network">Network</string>
    <string name="server_lab_more_function">Transport</string>
    <string name="server_lab_head_type">head type</string>
    <string name="server_lab_mode_type">gRPC mode</string>
    <string name="server_lab_request_host">host</string>
    <string name="server_lab_request_host_http">http host</string>
    <string name="server_lab_request_host_ws">ws host</string>
    <string name="server_lab_request_host_httpupgrade">httpupgrade host</string>
    <string name="server_lab_request_host_xhttp">xhttp host</string>
    <string name="server_lab_request_host_h2">h2 host</string>
    <string name="server_lab_request_host_quic">QUIC security</string>
    <string name="server_lab_request_host_grpc">gRPC Authority</string>
    <string name="server_lab_path">path</string>
    <string name="server_lab_path_ws">ws path</string>
    <string name="server_lab_path_httpupgrade">httpupgrade path</string>
    <string name="server_lab_path_xhttp">xhttp path</string>
    <string name="server_lab_path_h2">h2 path</string>
    <string name="server_lab_path_quic">QUIC key</string>
    <string name="server_lab_path_kcp">kcp seed</string>
    <string name="server_lab_path_grpc">gRPC serviceName</string>
    <string name="server_lab_stream_security">TLS</string>
    <string name="server_lab_stream_fingerprint">Fingerprint</string>
    <string name="server_lab_stream_alpn">Alpn</string>
    <string name="server_lab_allow_insecure">allowInsecure</string>
    <string name="server_lab_sni">SNI</string>
    <string name="server_lab_address3">address</string>
    <string name="server_lab_port3">port</string>
    <string name="server_lab_id3">password</string>
    <string name="server_lab_security3">security</string>
    <string name="server_lab_id4">Password(Optional)</string>
    <string name="server_lab_security4">User(Optional)</string>
    <string name="server_lab_encryption">encryption</string>
    <string name="server_lab_flow">flow</string>
    <string name="server_lab_public_key">PublicKey</string>
    <string name="server_lab_preshared_key">PreSharedKey(optional)</string>
    <string name="server_lab_short_id">ShortId</string>
    <string name="server_lab_spider_x">SpiderX</string>
    <string name="server_lab_secret_key">SecretKey</string>
    <string name="server_lab_reserved">Reserved(Optional, separated by commas)</string>
    <string name="server_lab_local_address">Local address (optional IPv4/IPv6, separated by commas)</string>
    <string name="server_lab_local_mtu">Mtu(optional, default 1420)</string>
    <string name="toast_success">Success</string>
    <string name="toast_failure">Failure</string>
    <string name="toast_none_data">There is nothing</string>
    <string name="toast_incorrect_protocol">Incorrect protocol</string>
    <string name="toast_decoding_failed">Decoding failed</string>
    <string name="title_file_chooser">Select a Config File</string>
    <string name="toast_require_file_manager">Please install a File Manager.</string>
    <string name="server_customize_config">Customize Config</string>
    <string name="toast_config_file_invalid">Invalid Config</string>
    <string name="server_lab_content">Content</string>
    <string name="toast_none_data_clipboard">There is no data in the clipboard</string>
    <string name="toast_invalid_url">Invalid URL</string>
    <string name="toast_insecure_url_protocol">Please do not use the insecure HTTP protocol subscription address</string>
    <string name="server_lab_need_inbound">Ensure inbounds port is consistent with the settings</string>
    <string name="toast_malformed_josn">Config malformed</string>
    <string name="server_lab_request_host6">Host(SNI)(Optional)</string>
    <string name="toast_action_not_allowed">Action not allowed</string>
    <string name="server_obfs_password">Obfs password</string>
    <string name="server_lab_port_hop">Port Hopping(will override the port)</string>
    <string name="server_lab_port_hop_interval">Port Hopping Interval</string>
    <string name="server_lab_stream_pinsha256">pinSHA256</string>
    <string name="server_lab_bandwidth_down">Bandwidth down (units)</string>
    <string name="server_lab_bandwidth_up">Bandwidth up (units)</string>
    <string name="server_lab_xhttp_mode">XHTTP Mode</string>
    <string name="server_lab_xhttp_extra">XHTTP Extra raw JSON, format: { XHTTPObject }</string>

    <!-- UserAssetActivity -->
    <string name="toast_asset_copy_failed">File copy failed, please use File Manager</string>
    <string name="menu_item_add_asset">Add asset</string>
    <string name="menu_item_add_file">Add files</string>
    <string name="menu_item_add_url">Add URL</string>
    <string name="menu_item_scan_qrcode">Scan QRcode</string>
    <string name="title_url">URL</string>
    <string name="menu_item_download_file">Download files</string>
    <string name="title_user_asset_add_url">Add asset URL</string>
    <string name="msg_file_not_found">File not found</string>
    <string name="msg_remark_is_duplicate">The remarks already exists</string>
    <string name="asset_geo_files_sources">Geo files source (optional)</string>

    <!-- PerAppProxyActivity -->
    <string name="msg_dialog_progress">Loading</string>
    <string name="menu_item_search">Search</string>
    <string name="menu_item_select_all">Select all</string>
    <string name="msg_enter_keywords">Enter keywords</string>
    <string name="switch_bypass_apps_mode">Bypass Mode</string>
    <string name="menu_item_select_proxy_app">Auto select proxy app</string>
    <string name="msg_downloading_content">Downloading content</string>
    <string name="menu_item_export_proxy_app">Export to Clipboard</string>
    <string name="menu_item_import_proxy_app">Import from Clipboard</string>
    <string name="per_app_proxy_settings">Per-app settings</string>
    <string name="per_app_proxy_settings_enable">Enable per-app</string>

    <!-- Preferences -->
    <string name="title_settings">Settings</string>
    <string name="title_advanced">Advanced Settings</string>
    <string name="title_vpn_settings">VPN Settings</string>
    <string name="title_pref_per_app_proxy">Per-app proxy</string>
    <string name="summary_pref_per_app_proxy">General: Checked apps use proxy, unchecked apps connect directly; \nBypass mode: checked apps connect directly, unchecked apps use proxy. \nThe option to automatically select proxy applications is in the menu</string>
    <string name="title_pref_is_booted">Auto connect at startup</string>
    <string name="summary_pref_is_booted">Automatically connects to the selected server at startup, which may be unsuccessful</string>

    <string name="title_mux_settings">Mux Settings</string>
    <string name="title_pref_mux_enabled">Enable Mux</string>
    <string name="summary_pref_mux_enabled">Faster, but it may cause unstable connectivity\nCustomize how to handle TCP, UDP and QUIC below</string>
    <string name="title_pref_mux_concurency">TCP connections（range -1 to 1024）</string>
    <string name="title_pref_mux_xudp_concurency">XUDP connections（range -1 to 1024）</string>
    <string name="title_pref_mux_xudp_quic">Handling of QUIC in mux tunnel</string>

    <string name="title_pref_speed_enabled">Enable speed display</string>
    <string name="summary_pref_speed_enabled">Display current speed in the notification.\nNotification icon would change based on
        usage.</string>

    <string name="title_pref_sniffing_enabled">Enable Sniffing</string>
    <string name="summary_pref_sniffing_enabled">Try sniff domain from the packet (default on)</string>
    <string name="title_pref_route_only_enabled">Enable routeOnly</string>
    <string name="summary_pref_route_only_enabled">Use the sniffed domain name for routing only, and keep the target address as the IP address.</string>

    <string name="title_pref_local_dns_enabled">Enable local DNS</string>
    <string name="summary_pref_local_dns_enabled">DNS processed by core‘s DNS module (Recommended if you need routing bypassing LAN and mainland addresses)</string>

    <string name="title_pref_fake_dns_enabled">Enable fake DNS</string>
    <string name="summary_pref_fake_dns_enabled">Local DNS returns fake IP addresses (faster, but it may not work for some apps)</string>

    <string name="title_pref_prefer_ipv6">Prefer IPv6</string>
    <string name="summary_pref_prefer_ipv6">Enable IPv6 routes and Prefer IPv6 addresses</string>

    <string name="title_pref_remote_dns">Remote DNS (udp/tcp/https/quic)(Optional)</string>
    <string name="summary_pref_remote_dns">DNS</string>

    <string name="title_pref_vpn_dns">VPN DNS (only IPv4/v6)</string>
    <string name="title_pref_vpn_bypass_lan">Does VPN bypass LAN</string>

    <string name="title_pref_domestic_dns">Domestic DNS (Optional)</string>
    <string name="summary_pref_domestic_dns">DNS</string>

    <string name="title_pref_dns_hosts">DNS hosts (Format: domain:address,…)</string>
    <string name="summary_pref_dns_hosts">domain:address,…</string>

    <string name="title_pref_delay_test_url">True delay test url (http/https)</string>
    <string name="summary_pref_delay_test_url">Url</string>

    <string name="title_pref_proxy_sharing_enabled">Allow connections from the LAN</string>
    <string name="summary_pref_proxy_sharing_enabled">Other devices can connect to proxy by your IP address through local proxy. Only enable in trusted networks to avoid unauthorized connections</string>
    <string name="toast_warning_pref_proxysharing_short">Allow connections from the LAN. Make sure you are in a trusted network</string>

    <string name="title_pref_allow_insecure">allowInsecure</string>
    <string name="summary_pref_allow_insecure">When TLS is selected, allow insecure connections by default</string>

    <string name="title_pref_socks_port">Local proxy port</string>
    <string name="summary_pref_socks_port">Local proxy port</string>

    <string name="title_pref_local_dns_port">Local DNS port</string>
    <string name="summary_pref_local_dns_port">Local DNS port</string>

    <string name="title_pref_confirm_remove">Delete configuration file confirmation</string>
    <string name="summary_pref_confirm_remove">Whether to delete the configuration file requires a second confirmation by the user</string>

    <string name="title_pref_start_scan_immediate">Start scanning immediately</string>
    <string name="summary_pref_start_scan_immediate">Open the camera to scan immediately at startup, otherwise you can choose to scan the code or select a photo in the toolbar</string>

    <string name="title_pref_append_http_proxy">Append HTTP Proxy to VPN</string>
    <string name="summary_pref_append_http_proxy">HTTP proxy will be used directly from (browser/ some supported apps), without going through the virtual NIC device (Android 10+)</string>

    <string name="title_pref_double_column_display">Enable double column display</string>
    <string name="summary_pref_double_column_display">The profile list is displayed in double columns, allowing more content to be displayed on the screen. You need to restart the application to take effect.</string>

    <!-- AboutActivity -->
    <string name="title_pref_feedback">Feedback</string>
    <string name="summary_pref_feedback">Feedback enhancements or bugs to GitHub</string>
    <string name="summary_pref_tg_group">Join Telegram Group</string>
    <string name="toast_tg_app_not_found">Telegram app not found</string>
    <string name="title_privacy_policy">Privacy policy</string>
    <string name="title_about">About</string>
    <string name="title_source_code">Source code</string>
    <string name="title_oss_license">Open Source licenses</string>
    <string name="title_tg_channel">Telegram channel</string>
    <string name="title_configuration_backup">Backup configuration</string>
    <string name="summary_configuration_backup">Storage location: [%s], The backup will be cleared after uninstalling the app or clearing the storage</string>
    <string name="title_configuration_restore">Restore configuration</string>
    <string name="title_configuration_share">Share configuration</string>



    <string name="title_pref_auto_update_subscription">Automatic update subscriptions</string>
    <string name="summary_pref_auto_update_subscription">Update your subscriptions automatically at set intervals in the background. Depending on the device, this feature may not always work</string>
    <string name="title_pref_auto_update_interval">Auto Update Interval (Minutes, Min value 15)</string>

    <string name="title_core_loglevel">Log Level</string>
    <string name="title_mode">Mode</string>
    <string name="title_mode_help">Click me for more help</string>
    <string name="title_language">Language</string>
    <string name="title_ui_settings">UI settings</string>
    <string name="title_pref_ui_mode_night">UI mode settings</string>

    <string name="title_logcat">Logcat</string>
    <string name="logcat_copy">Copy</string>
    <string name="logcat_clear">Clear</string>
    <string name="title_service_restart">Service restart</string>
    <string name="title_del_all_config">Delete current group configuration</string>
    <string name="title_del_duplicate_config">Delete current group duplicate configuration</string>
    <string name="title_del_invalid_config">Delete current group invalid configuration</string>
    <string name="title_export_all">Export current group non-custom configs to clipboard</string>
    <string name="title_servers_management">Servers Management</string>
    <string name="title_sub_setting">Subscription group setting</string>
    <string name="sub_setting_remarks">remarks</string>
    <string name="sub_setting_url">Optional URL</string>
    <string name="sub_setting_filter">Remarks regular filter</string>
    <string name="sub_setting_enable">Enable update</string>
    <string name="sub_auto_update">Enable automatic update</string>
    <string name="sub_allow_insecure_url">Allow insecure HTTP address</string>
    <string name="sub_setting_pre_profile">Previous proxy configuration remarks</string>
    <string name="sub_setting_next_profile">Next proxy configuration remarks</string>
    <string name="sub_setting_pre_profile_tip">The configuration remarks exists and is unique</string>
    <string name="title_sub_update">Update current group subscription</string>
    <string name="title_ping_all_server">Tcping current group configuration</string>
    <string name="title_real_ping_all_server">Real delay current group configuration</string>
    <string name="title_user_asset_setting">Asset files</string>
    <string name="title_sort_by_test_results">Sorting by test results</string>
    <string name="title_filter_config">Filter configuration file</string>
    <string name="filter_config_all">All groups</string>
    <string name="title_del_duplicate_config_count">Delete %d duplicate configurations</string>
    <string name="title_del_config_count">Delete %d configurations</string>
    <string name="title_import_config_count">Import %d configurations</string>
    <string name="title_export_config_count">Export %d configurations</string>
    <string name="title_update_config_count">Update %d configurations</string>

    <string name="tasker_start_service">Start Service</string>
    <string name="tasker_setting_confirm">Confirm</string>

    <!-- RoutingSettingActivity -->
    <string name="routing_settings_domain_strategy">Domain strategy</string>
    <string name="routing_settings_title">Routing Settings</string>
    <string name="routing_settings_tips">Separated by commas(,), choose domain or ip</string>
    <string name="routing_settings_save">Save</string>
    <string name="routing_settings_delete">Clear</string>
    <string name="routing_settings_rule_title">Routing Rule Settings</string>
    <string name="routing_settings_add_rule">Add rule</string>
    <string name="routing_settings_import_predefined_rulesets">Import predefined rulesets</string>
    <string name="routing_settings_import_rulesets_tip">Existing rulesets will be deleted, are you sure to continue?</string>
    <string name="routing_settings_import_rulesets_from_clipboard">Import ruleset from clipboard</string>
    <string name="routing_settings_import_rulesets_from_qrcode">Import ruleset from QRcode</string>
    <string name="routing_settings_export_rulesets_to_clipboard">Export ruleset to clipboard</string>
    <string name="routing_settings_locked">Locked, keep this rule when import presets</string>
    <string name="routing_settings_domain">domain</string>
    <string name="routing_settings_ip">ip</string>
    <string name="routing_settings_port">port</string>
    <string name="routing_settings_protocol">protocol</string>
    <string name="routing_settings_protocol_tip" translatable="false">[http,tls,bittorrent]</string>
    <string name="routing_settings_network">network</string>
    <string name="routing_settings_network_tip" translatable="false">[udp|tcp]</string>
    <string name="routing_settings_outbound_tag">outboundTag</string>

    <string name="connection_test_pending">Check Connectivity</string>
    <string name="connection_test_testing">Testing…</string>
    <string name="connection_test_testing_count">Testing %d configurations…</string>
    <string name="connection_test_available">Success: Connection took %dms</string>
    <string name="connection_test_error">Fail to detect internet connection: %s</string>
    <string name="connection_test_fail">Internet Unavailable</string>
    <string name="connection_test_error_status_code">Error code: #%d</string>
    <string name="connection_not_connected">Not connected</string>

    <string name="import_subscription_success">Subscription imported Successfully</string>
    <string name="import_subscription_failure">Import subscription failed</string>
    <string name="title_fragment_settings">Fragment Settings</string>
    <string name="title_pref_fragment_packets">Fragment Packets</string>
    <string name="title_pref_fragment_length">Fragment Length (min-max)</string>
    <string name="title_pref_fragment_interval">Fragment Interval (min-max)</string>
    <string name="title_pref_fragment_enabled">Enable Fragment</string>

    <string name="update_check_for_update">Check for update</string>
    <string name="update_already_latest_version">Already on the latest version</string>
    <string name="update_new_version_found">New version found: %s</string>
    <string name="update_now">Update now</string>
    <string name="update_check_pre_release">Check Pre-release</string>
    <string name="update_checking_for_update">Checking for update…</string>

    <!-- Connection Status -->
    <string name="connection_connected">Connected</string>
    <string name="connection_disconnected">Disconnected</string>
    <string name="connection_connecting">Connecting...</string>
    <string name="connection_disconnecting">Disconnecting...</string>

    <!-- Connection Button -->
    <string name="connect">Connect</string>
    <string name="disconnect">Disconnect</string>

    <!-- Server Selection -->
    <string name="server_selection">Server Selection</string>
    <string name="server_selection_auto">Auto</string>
    <string name="server_selection_manual">Manual</string>
    <string name="select_server">Select Server</string>
    <string name="no_servers_available">No servers available</string>

    <!-- Common Actions -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="save">Save</string>
    <string name="share">Share</string>
    <string name="copy">Copy</string>
    <string name="paste">Paste</string>
    <string name="test">Test</string>
    <string name="add">Add</string>
    <string name="remove">Remove</string>
    <string name="clear">Clear</string>
    <string name="refresh">Refresh</string>
    <string name="import_config">Import Config</string>
    <string name="export_config">Export Config</string>

    <!-- Messages -->
    <string name="msg_action_running">Action running</string>
    <string name="msg_action_success">Action successful</string>
    <string name="msg_action_failed">Action failed</string>
    <string name="msg_del_config_comfirm">Delete this config?</string>
    <string name="msg_del_config_success">Config deleted successfully</string>
    <string name="msg_del_config_failed">Failed to delete config</string>

    <!-- Server Configuration -->
    <string name="server_lab_alterId">Alter ID</string>

    <!-- QR Code -->
    <string name="title_scanner">QR Scanner</string>
    <string name="msg_scan_qrcode">Scan QR code for config</string>
    <string name="msg_scan_qrcode_success">QR code scanned successfully</string>
    <string name="msg_scan_qrcode_failed">Failed to scan QR code</string>

    <!-- About -->
    <string name="about_version">Version</string>
    <string name="about_source_code">Source Code</string>
    <string name="about_telegram">Telegram</string>
    <string name="about_privacy_policy">Privacy Policy</string>
    <string name="about_feedback">Feedback</string>

    <!-- Additional Titles -->
    <string name="title_bypass_list">Bypass List</string>
    <string name="title_promotion">Promotion</string>
    <string name="title_routing">Routing</string>
    <string name="title_pref_mux_xudp_concorency">XUDP Concurrency</string>

    <!-- Language Options -->
    <string name="language_english">English</string>
    <string name="language_arabic_egypt">العربية (مصر)</string>

    <!-- Restart Dialog -->
    <string name="restart_required_title">Restart Required</string>
    <string name="restart_required_message">Language change requires app restart to take effect.</string>
    <string name="restart_now">Restart Now</string>
    <string name="restart_later">Later</string>

    <!-- Missing strings from warnings -->
    <string name="connection_test_error_timeout">Timeout</string>
    <string name="logcat_clear_success">Log cleared successfully</string>
    <string name="logcat_copy_failed">Log copy failed</string>
    <string name="logcat_copy_success">Log copied successfully</string>
    <string name="menu_item_delete_config">Delete Config</string>
    <string name="menu_item_edit_config">Edit Config</string>
    <string name="menu_item_share_config">Share Config</string>
    <string name="menu_item_test_config">Test Config</string>
    <string name="msg_enter_bypass_list_url">Enter bypass list URL</string>
    <string name="msg_enter_subscription_url">Enter subscription URL</string>
    <string name="msg_subscription_empty">Subscription is empty</string>
    <string name="msg_subscription_failed">Subscription update failed</string>
    <string name="msg_subscription_success">Subscription updated successfully</string>
    <string name="routing_settings_save_success">Routing settings saved successfully</string>
    <string name="summary_pref_per_app_proxy_tips">Apply proxy to specific apps only</string>
    <string name="title_bypass_list_url">Bypass list URL</string>
    <string name="title_pref_per_app_proxy_tips">Per app proxy tips</string>
    <string name="title_routing_custom">Custom routing</string>
    <string name="title_routing_domain_strategy">Domain strategy</string>
    <string name="title_routing_mode">Routing mode</string>
    <string name="title_routing_settings">Routing settings</string>
    <string name="title_subscription_setting">Subscription setting</string>
    <string name="title_subscription_url">Subscription URL</string>
    <string name="toast_config_file_not_found">Config file not found</string>
    <string name="toast_subscription_failed">Subscription failed</string>
    <string name="toast_subscription_success">Subscription success</string>

    <!-- Import/Export Options -->
    <string name="import_config_from_qrcode">Import config from QRcode</string>
    <string name="import_config_from_clipboard">Import config from Clipboard</string>
    <string name="import_config_from_locally">Import config from locally</string>

    <!-- Manual Configuration Types -->
    <string name="type_manually_vmess">Type manually[VMess]</string>
    <string name="type_manually_vless">Type manually[VLESS]</string>
    <string name="type_manually_shadowsocks">Type manually[Shadowsocks]</string>
    <string name="type_manually_socks">Type manually[SOCKS]</string>
    <string name="type_manually_http">Type manually[HTTP]</string>
    <string name="type_manually_trojan">Type manually[Trojan]</string>
    <string name="type_manually_wireguard">Type manually[Wireguard]</string>
    <string name="type_manually_hysteria2">Type manually[Hysteria2]</string>

    <!-- Connection Status -->
    <string name="not_connected">Not connected</string>
    <string name="connected">Connected</string>
    <string name="connecting">Connecting</string>
    <string name="disconnecting">Disconnecting</string>

    <!-- Action Messages -->
    <string name="action_not_allowed">Action not allowed</string>
    <string name="delete_confirm_title">Delete Confirmation</string>
    <string name="delete_confirm_message">Are you sure you want to delete this server?</string>
    <string name="duplicate">Duplicate</string>
    <string name="move_up">Move up</string>
    <string name="move_down">Move down</string>
    <string name="deselect_all">Deselect all</string>
    <string name="servers">Servers</string>
    <string name="server_management">Server Management</string>
    <string name="add_server">Add Server</string>
    <string name="server_list">Server List</string>
    <string name="no_servers">No servers available</string>
    <string name="server_added">Server added successfully</string>
    <string name="server_updated">Server updated successfully</string>
    <string name="server_deleted">Server deleted successfully</string>
    <string name="server_test_success">Server test successful</string>
    <string name="server_test_failed">Server test failed</string>

    <!-- File and Content -->

    <!-- Additional UI Elements -->

    <!-- Connection and Status -->
    <string name="connection_status">Connection Status</string>
    <string name="server_status">Server Status</string>
    <string name="network_status">Network Status</string>
    <string name="speed_test">Speed Test</string>
    <string name="latency">Latency</string>
    <string name="download_speed">Download Speed</string>
    <string name="upload_speed">Upload Speed</string>
    <string name="connection_statistics">Connection Statistics</string>
    <string name="duration">Duration</string>
    <string name="session_data">Session Data</string>

    <!-- Menu and Navigation -->
    <string name="menu">Menu</string>
    <string name="back">Back</string>
    <string name="forward">Forward</string>
    <string name="home">Home</string>
    <string name="search">Search</string>
    <string name="filter">Filter</string>
    <string name="sort">Sort</string>
    <string name="sort_by_name">Sort by name</string>
    <string name="sort_by_date">Sort by date</string>
    <string name="sort_by_speed">Sort by speed</string>

    <!-- Additional Server Management -->
    <string name="server_info">Server Info</string>
    <string name="server_config">Server Config</string>
    <string name="server_details">Server Details</string>
    <string name="server_address">Server Address</string>
    <string name="server_port">Server Port</string>
    <string name="server_protocol">Server Protocol</string>
    <string name="server_encryption">Server Encryption</string>
    <string name="server_network">Server Network</string>
    <string name="server_security">Server Security</string>
    <string name="server_remarks">Server Remarks</string>
    <string name="server_subscription">Server Subscription</string>

    <!-- Additional Actions -->
    <string name="action_select">Select</string>
    <string name="action_deselect">Deselect</string>
    <string name="action_move">Move</string>
    <string name="action_rename">Rename</string>
    <string name="action_clone">Clone</string>
    <string name="action_export">Export</string>
    <string name="action_import">Import</string>
    <string name="action_backup">Backup</string>
    <string name="action_restore">Restore</string>
    <string name="action_reset">Reset</string>
    <string name="action_clear">Clear</string>
    <string name="action_refresh">Refresh</string>
    <string name="action_reload">Reload</string>
    <string name="action_retry">Retry</string>
    <string name="action_stop">Stop</string>
    <string name="action_start">Start</string>
    <string name="action_restart">Restart</string>
    <string name="action_pause">Pause</string>
    <string name="action_resume">Resume</string>

    <!-- Missing strings that exist in Arabic but not in English -->
    <string name="title_dns_settings">DNS Settings</string>
    <string name="select_all">Select All</string>
    <string name="server_lab_request_host2">Request Host (Optional)</string>
    <string name="server_lab_request_host3">Request Host (Optional)</string>
    <string name="server_lab_request_host4">Request Host (Optional)</string>
    <string name="server_lab_request_host5">Request Host (Optional)</string>
    <string name="server_lab_stream_utls">uTLS</string>
    <string name="server_lab_stream_xtls">XTLS</string>
    <string name="server_lab_stream_xtls_flow">XTLS Flow</string>
    <string name="server_lab_stream_xtls_seed">XTLS Seed</string>
    <string name="server_lab_stream_xtls_dest">XTLS Dest</string>
    <string name="server_lab_stream_xtls_spider_x">SpiderX</string>
    <string name="server_lab_stream_xtls_spider_y">SpiderY</string>
    <string name="server_lab_stream_xtls_return">XTLS Return</string>

    <!-- Authentication Strings -->
    <string name="welcome_back">Welcome back!</string>
    <string name="email">Email</string>
    <string name="password">Password</string>
    <string name="username">Username</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="login">Login</string>
    <string name="register">Register</string>
    <string name="create_account">Create Account</string>
    <string name="back_to_login">Back to Login</string>
    <string name="forgot_password">Forgot Password?</string>

    <!-- Validation Messages -->
    <string name="email_required">Email is required</string>
    <string name="invalid_email">Please enter a valid email</string>
    <string name="password_required">Password is required</string>
    <string name="password_too_short">Password must be at least 6 characters</string>
    <string name="username_required">Username is required</string>
    <string name="username_too_short">Username must be at least 3 characters</string>
    <string name="confirm_password_required">Please confirm your password</string>
    <string name="passwords_do_not_match">Passwords do not match</string>

    <!-- Authentication Results -->
    <string name="login_success">Welcome back, %s!</string>
    <string name="login_failed">Login failed</string>
    <string name="registration_success">Account created successfully!</string>
    <string name="registration_failed">Registration failed</string>

    <!-- User Status Messages -->
    <string name="subscription_expired">Your subscription has expired</string>
    <string name="data_quota_exceeded">You have exceeded your data quota</string>
    <string name="account_inactive">Your account is inactive</string>
    <string name="feature_coming_soon">Feature coming soon</string>

    <!-- Admin Panel -->
    <string name="admin_panel">Admin Panel</string>
    <string name="user_management">User Management</string>
    <string name="usage_statistics">Usage Statistics</string>
    <string name="data_quota">Data Quota</string>
    <string name="remaining_data">Remaining Data</string>
    <string name="subscription_expiry">Subscription Expiry</string>
    <string name="user_status">User Status</string>
    <string name="active">Active</string>
    <string name="inactive">Inactive</string>
    <string name="expired">Expired</string>
    <string name="quota_exceeded">Quota Exceeded</string>

    <!-- Additional Admin Panel -->
    <string name="admin_panel_description">Manage users, servers, and system settings</string>
    <string name="system_settings">System Settings</string>
    <string name="access_denied">Access denied - Admin privileges required</string>

    <!-- User Management -->
    <string name="add_user">Add User</string>
    <string name="no_users_found">No users found</string>
    <string name="data_usage">Data Usage</string>
    <string name="expiry_date">Expiry Date</string>
    <string name="no_expiry">No Expiry</string>
    <string name="admin">Admin</string>

    <!-- Usage Statistics -->
    <string name="total_users">Total Users</string>
    <string name="active_users">Active Users</string>
    <string name="total_data_used">Total Data Used</string>
    <string name="total_sessions">Total Sessions</string>
    <string name="today">Today</string>
    <string name="this_week">This Week</string>
    <string name="this_month">This Month</string>
    <string name="all_time">All Time</string>
    <string name="no_usage_data">No usage data available</string>
    <string name="unknown_date">Unknown Date</string>
    <string name="active_session">Active</string>
    <string name="completed_session">Completed</string>
    <string name="unknown_device">Unknown Device</string>
    <string name="unknown_version">Unknown Version</string>

    <!-- System Settings -->
    <string name="system_statistics">System Statistics</string>
    <string name="total_servers">Total Servers</string>
    <string name="usage_records">Usage Records</string>
    <string name="default_settings">Default Settings</string>
    <string name="default_data_quota_gb">Default Data Quota (GB)</string>
    <string name="default_subscription_days">Default Subscription (Days)</string>
    <string name="system_options">System Options</string>
    <string name="auto_server_selection">Auto Server Selection</string>
    <string name="auto_server_selection_desc">Automatically select best server for users</string>
    <string name="maintenance_mode">Maintenance Mode</string>
    <string name="maintenance_mode_desc">Disable new connections during maintenance</string>
    <string name="data_management">Data Management</string>
    <string name="clear_usage_data">Clear Usage Data</string>
    <string name="export_data">Export Data</string>
    <string name="reset_all_settings">Reset All Settings</string>
    <string name="invalid_quota_value">Please enter a valid quota value</string>
    <string name="invalid_subscription_days">Please enter valid subscription days</string>
    <string name="clear_usage_data_confirmation">Are you sure you want to clear all usage data? This action cannot be undone.</string>
    <string name="reset_settings_confirmation">Are you sure you want to reset all settings to defaults?</string>
    <string name="confirm">Confirm</string>
    <string name="reset_settings">Reset Settings</string>

    <!-- Server Management -->
    <string name="no_servers_found">No servers found</string>
    <string name="read_only">Read Only</string>
    <string name="priority">Priority</string>
    <string name="users">Users</string>
    <string name="all_users">All Users</string>



    <!-- Notification Channels -->
    <string name="channel_vpn_status">VPN Status</string>
    <string name="channel_vpn_status_desc">Shows VPN connection status</string>
    <string name="channel_alerts">Alerts</string>
    <string name="channel_alerts_desc">Important alerts and warnings</string>
    <string name="channel_usage">Usage Notifications</string>
    <string name="channel_usage_desc">Data usage and quota notifications</string>

    <!-- VPN Status Notifications -->
    <string name="vpn_connected">VPN Connected</string>
    <string name="vpn_disconnected">VPN Disconnected</string>
    <string name="connected_to_server">Connected to %s</string>
    <string name="tap_to_connect">Tap to connect</string>
    <string name="connection_stats_notification" formatted="false">↑ %s ↓ %s</string>

    <!-- Quota Notifications -->
    <string name="quota_warning_title">Data Quota Warning</string>
    <string name="quota_warning_message" formatted="false">%d%% used - %.2f GB remaining</string>
    <string name="quota_warning_detailed" formatted="false">You have used %d%% of your data quota. %.2f GB remaining. Consider monitoring your usage to avoid exceeding your limit.</string>
    <string name="quota_exceeded_title">Data Quota Exceeded</string>
    <string name="quota_exceeded_message">Your data quota has been exceeded</string>
    <string name="quota_exceeded_detailed">You have exceeded your data quota limit. VPN connections may be restricted until your quota is renewed.</string>

    <!-- Subscription Notifications -->
    <string name="subscription_expiring_title">Subscription Expiring</string>
    <string name="subscription_expiring_message" formatted="false">%d days remaining</string>
    <string name="subscription_expiring_detailed" formatted="false">Your subscription will expire in %d days. Please renew to continue using the VPN service.</string>
    <string name="subscription_expired_title">Subscription Expired</string>
    <string name="subscription_expired_message">Your subscription has expired</string>
    <string name="subscription_expired_detailed">Your VPN subscription has expired. Please contact support to renew your subscription.</string>

</resources>
