<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".ui.AdminPanelActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIconTint="?attr/colorOnPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Welcome Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="12dp"
                        android:src="@drawable/ic_admin_panel_24dp"
                        android:tint="?attr/colorPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/admin_panel"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="4dp"
                        android:text="@string/admin_panel_description"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Management Cards Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- First Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <!-- User Management Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_user_management"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="6dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:foreground="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_person_24dp"
                                android:tint="?attr/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/user_management"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorOnSurface"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Server Management Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_server_management"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="6dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:foreground="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_server_24dp"
                                android:tint="?attr/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/server_management"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorOnSurface"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Second Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Usage Statistics Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_usage_statistics"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="6dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:foreground="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_analytics_24dp"
                                android:tint="?attr/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/usage_statistics"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorOnSurface"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- System Settings Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_system_settings"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="6dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:foreground="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_settings_24dp"
                                android:tint="?attr/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/system_settings"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorOnSurface"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
