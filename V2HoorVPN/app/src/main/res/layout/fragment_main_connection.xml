<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="?attr/colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- User Status Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:id="@+id/text_view_username"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="محمد أحمد" />

                    <TextView
                        android:id="@+id/text_view_subscription_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:padding="6dp"
                        android:background="@drawable/status_background"
                        tools:text="نشط" />

                </LinearLayout>

                <!-- Data Usage Progress -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/data_usage"
                            android:textSize="14sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_data_usage"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface"
                            android:gravity="end"
                            tools:text="2.5 / 10.0 GB" />

                    </LinearLayout>

                    <ProgressBar
                        android:id="@+id/progress_bar_data_usage"
                        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginTop="8dp"
                        android:progressTint="?attr/colorPrimary"
                        android:progressBackgroundTint="?attr/colorSurfaceVariant"
                        tools:progress="25" />

                </LinearLayout>

                <!-- Additional Info -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/remaining_data"
                            android:textSize="12sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_remaining_data"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorPrimary"
                            tools:text="7.5 GB" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="end">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/expiry_date"
                            android:textSize="12sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_expiry_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface"
                            tools:text="31/12/2024" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Connection Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_marginBottom="24dp">

            <!-- Connection Status -->
            <TextView
                android:id="@+id/text_view_connection_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textSize="16sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:gravity="center"
                tools:text="غير متصل" />

            <!-- Large Circular Connection Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_connection"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginBottom="24dp"
                android:text="@string/connect"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnPrimary"
                app:cornerRadius="100dp"
                app:backgroundTint="?attr/colorPrimary"
                android:elevation="8dp" />

            <!-- Loading Progress -->
            <ProgressBar
                android:id="@+id/progress_bar_loading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Server Selection Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/server_selection"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_refresh_servers"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:icon="@drawable/ic_refresh_24dp" />

                </LinearLayout>

                <!-- Auto Selection Switch -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/auto_server_selection"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurface" />

                    <com.google.android.material.materialswitch.MaterialSwitch
                        android:id="@+id/switch_auto_selection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- Server Dropdown -->
                <Spinner
                    android:id="@+id/spinner_server_selection"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/spinner_background"
                    android:padding="12dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Connection Statistics Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_connection_stats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/connection_statistics"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Upload Speed -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/upload_speed"
                            android:textSize="12sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_upload_speed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorPrimary"
                            tools:text="125 KB/s" />

                    </LinearLayout>

                    <!-- Download Speed -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/download_speed"
                            android:textSize="12sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_download_speed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorPrimary"
                            tools:text="850 KB/s" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <!-- Duration -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/duration"
                            android:textSize="12sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_connection_duration"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface"
                            tools:text="02:45:30" />

                    </LinearLayout>

                    <!-- Session Data -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/session_data"
                            android:textSize="12sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/text_view_session_data"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorOnSurface"
                            tools:text="125.5 MB" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
