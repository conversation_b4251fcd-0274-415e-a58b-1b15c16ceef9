<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_view_session_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    tools:text="abc12345" />

                <TextView
                    android:id="@+id/text_view_user_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="User: def67890" />

            </LinearLayout>

            <TextView
                android:id="@+id/text_view_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:padding="4dp"
                android:background="@drawable/status_background"
                tools:text="Active" />

        </LinearLayout>

        <!-- Data Usage Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_data_usage_24dp"
                android:tint="?attr/colorPrimary" />

            <TextView
                android:id="@+id/text_view_data_usage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="?attr/colorPrimary"
                tools:text="125.5 MB" />

            <TextView
                android:id="@+id/text_view_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="02:45:30" />

        </LinearLayout>

        <!-- Upload/Download Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/text_view_upload"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="↑ 25.2 MB" />

            <TextView
                android:id="@+id/text_view_download"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:gravity="end"
                tools:text="↓ 100.3 MB" />

        </LinearLayout>

        <!-- Date and Device Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_calendar_24dp"
                android:tint="?attr/colorOnSurfaceVariant" />

            <TextView
                android:id="@+id/text_view_date_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="25/12/2024 14:30" />

            <TextView
                android:id="@+id/text_view_device_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Samsung Galaxy S21" />

        </LinearLayout>

        <!-- App Version (if available) -->
        <TextView
            android:id="@+id/text_view_app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="10sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            tools:text="v1.0.0"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
