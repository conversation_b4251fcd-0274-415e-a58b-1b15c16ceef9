<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@android:color/white"
    tools:context=".ui.LoginActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- App Logo -->
        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="48dp"
            android:src="@mipmap/ic_launcher"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- App Title -->
        <TextView
            android:id="@+id/tv_app_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/app_name"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_logo" />

        <!-- Welcome Text -->
        <TextView
            android:id="@+id/tv_welcome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/welcome_back"
            android:textSize="16sp"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_app_title" />

        <!-- Email Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:hint="@string/email"
            app:startIconDrawable="@drawable/ic_email_24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_welcome">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_password"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="@string/password"
            app:startIconDrawable="@drawable/ic_lock_24dp"
            app:endIconMode="password_toggle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/til_email">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Forgot Password -->
        <TextView
            android:id="@+id/tv_forgot_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/forgot_password"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/til_password" />

        <!-- Login Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_login"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginTop="24dp"
            android:text="@string/login"
            android:textSize="16sp"
            app:cornerRadius="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_forgot_password" />

        <!-- Register Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_register"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginTop="8dp"
            android:text="@string/create_account"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_login" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/btn_login"
            app:layout_constraintEnd_toEndOf="@id/btn_login"
            app:layout_constraintStart_toStartOf="@id/btn_login"
            app:layout_constraintTop_toTopOf="@id/btn_login" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
