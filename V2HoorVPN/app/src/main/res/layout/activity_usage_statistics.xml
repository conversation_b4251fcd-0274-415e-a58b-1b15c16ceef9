<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".ui.UsageStatisticsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIconTint="?attr/colorOnPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Statistics Summary Cards -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <!-- Total Users Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/text_view_total_users"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorPrimary"
                                tools:text="150" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_users"
                                android:textSize="12sp"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Active Users Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/text_view_active_users"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorPrimary"
                                tools:text="120" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/active_users"
                                android:textSize="12sp"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Second Row of Stats -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp">

                    <!-- Total Data Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/text_view_total_data"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorPrimary"
                                tools:text="1.2 TB" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_data_used"
                                android:textSize="12sp"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Total Sessions Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/text_view_total_sessions"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="?attr/colorPrimary"
                                tools:text="2,450" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_sessions"
                                android:textSize="12sp"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:gravity="center" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Filter Chips -->
                <com.google.android.material.chip.ChipGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_today"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/today" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_week"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/this_week" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_month"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/this_month" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/all_time"
                        android:checked="true" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Usage Records List -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_view_usage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_usage_statistics" />

                    <TextView
                        android:id="@+id/text_view_empty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@string/no_usage_data"
                        android:textSize="16sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:visibility="gone" />

                    <ProgressBar
                        android:id="@+id/progress_bar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone" />

                </FrameLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
