<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="12dp">

    <TextView
        android:id="@+id/text_view_server_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="16sp"
        android:textColor="?attr/colorOnSurface"
        android:ellipsize="end"
        android:maxLines="1"
        tools:text="US Server 1" />

    <TextView
        android:id="@+id/text_view_server_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textSize="12sp"
        android:textColor="?attr/colorOnSurfaceVariant"
        tools:text="New York" />

</LinearLayout>
