<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_view_server_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                tools:text="US Server 1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/text_view_server_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    tools:text="VMESS" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="•"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/text_view_server_location"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="New York, USA" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end">

            <TextView
                android:id="@+id/text_view_server_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:drawableStart="@drawable/ic_speed_24dp"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="100 Mbps"
                android:visibility="gone" />

            <TextView
                android:id="@+id/text_view_server_latency"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:drawableStart="@drawable/ic_timer_24dp"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="25 ms"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
