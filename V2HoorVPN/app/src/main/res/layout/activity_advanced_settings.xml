<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.AdvancedSettingsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Connection Settings -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/connection_settings"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Auto Connect -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchAutoConnect"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/auto_connect"
                        android:layout_marginBottom="8dp" />

                    <!-- Kill Switch -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchKillSwitch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/kill_switch"
                        android:layout_marginBottom="8dp" />

                    <!-- Background Connection -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchBackgroundConnection"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/background_connection"
                        android:layout_marginBottom="16dp" />

                    <!-- Connection Timeout -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/connection_timeout"
                        android:layout_marginBottom="8dp"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etConnectionTimeout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSaveTimeout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/save"
                        android:layout_gravity="end"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- DNS Settings -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/dns_settings"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Custom DNS -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchCustomDns"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/custom_dns"
                        android:layout_marginBottom="16dp" />

                    <!-- DNS Servers Layout -->
                    <LinearLayout
                        android:id="@+id/layoutDnsServers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/primary_dns"
                            android:layout_marginBottom="8dp"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etPrimaryDns"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textNoSuggestions" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/secondary_dns"
                            android:layout_marginBottom="8dp"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etSecondaryDns"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textNoSuggestions" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnSaveDns"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/save"
                            android:layout_gravity="end"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Protocol Settings -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/protocol_settings"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Protocol Selection -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/preferred_protocol"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/spinnerProtocol"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Notification Settings -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/notification_settings"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Notifications -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchNotifications"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/enable_notifications"
                        android:layout_marginBottom="8dp" />

                    <!-- Quota Alerts -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchQuotaAlerts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/quota_alerts"
                        android:layout_marginBottom="8dp" />

                    <!-- Subscription Alerts -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchSubscriptionAlerts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/subscription_alerts"
                        android:layout_marginBottom="16dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Data Management -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/data_management"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Data Saving Mode -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchDataSaving"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/data_saving_mode"
                        android:layout_marginBottom="16dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Reset Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnResetAdvancedSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/reset_to_defaults"
                android:layout_marginTop="16dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
