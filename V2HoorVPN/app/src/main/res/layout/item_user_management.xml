<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_view_username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    tools:text="john_doe" />

                <TextView
                    android:id="@+id/text_view_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="<EMAIL>" />

            </LinearLayout>

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_admin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/admin"
                android:textSize="12sp"
                app:chipBackgroundColor="?attr/colorPrimary"
                app:chipStrokeWidth="0dp"
                android:textColor="?attr/colorOnPrimary"
                android:visibility="gone" />

            <TextView
                android:id="@+id/text_view_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textSize="12sp"
                android:textStyle="bold"
                android:padding="4dp"
                android:background="@drawable/status_background"
                tools:text="Active" />

        </LinearLayout>

        <!-- Data Usage Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/data_usage"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/text_view_data_usage"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    android:gravity="end"
                    tools:text="2.5 / 10.0 GB" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/progress_bar_data_usage"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="4dp"
                android:progressTint="?attr/colorPrimary"
                android:progressBackgroundTint="?attr/colorSurfaceVariant"
                tools:progress="25" />

        </LinearLayout>

        <!-- Expiry Date -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_calendar_24dp"
                android:tint="?attr/colorOnSurfaceVariant" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/expiry_date"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <TextView
                android:id="@+id/text_view_expiry"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurface"
                android:gravity="end"
                tools:text="31/12/2024" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
