<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/text_view_server_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="US Server 1" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_read_only"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/read_only"
                        android:textSize="10sp"
                        app:chipBackgroundColor="?attr/colorSurfaceVariant"
                        app:chipStrokeWidth="0dp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:visibility="gone" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:id="@+id/text_view_server_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="?attr/colorPrimary"
                        android:textStyle="bold"
                        tools:text="VMESS" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="•"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:id="@+id/text_view_location"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        tools:text="New York, USA" />

                </LinearLayout>

            </LinearLayout>

            <com.google.android.material.materialswitch.MaterialSwitch
                android:id="@+id/switch_active"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Status and Priority Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/text_view_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:padding="4dp"
                android:background="@drawable/status_background"
                tools:text="Active" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/priority"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <TextView
                android:id="@+id/text_view_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                tools:text="10" />

        </LinearLayout>

        <!-- Users Usage Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/users"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/text_view_users"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    android:gravity="end"
                    tools:text="15 / 50" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/progress_bar_users"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="4dp"
                android:progressTint="?attr/colorPrimary"
                android:progressBackgroundTint="?attr/colorSurfaceVariant"
                tools:progress="30" />

        </LinearLayout>

        <!-- Performance Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/text_view_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:drawableStart="@drawable/ic_speed_24dp"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="100 Mbps"
                android:visibility="gone" />

            <TextView
                android:id="@+id/text_view_latency"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:drawableStart="@drawable/ic_timer_24dp"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="25 ms"
                android:visibility="gone" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/text_view_allowed_users"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="11sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:drawableStart="@drawable/ic_group_24dp"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                tools:text="All Users" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
