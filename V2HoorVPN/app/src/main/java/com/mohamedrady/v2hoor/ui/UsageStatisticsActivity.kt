package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityUsageStatisticsBinding
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.viewmodel.UsageStatisticsViewModel

class UsageStatisticsActivity : BaseActivity() {
    
    private lateinit var binding: ActivityUsageStatisticsBinding
    private val viewModel: UsageStatisticsViewModel by viewModels()
    private lateinit var adapter: UsageStatisticsAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUsageStatisticsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupUI()
        observeViewModel()
        
        // Load statistics
        viewModel.loadStatistics()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.usage_statistics)
        }
    }

    private fun setupRecyclerView() {
        adapter = UsageStatisticsAdapter()
        
        binding.recyclerViewUsage.apply {
            layoutManager = LinearLayoutManager(this@UsageStatisticsActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupUI() {
        binding.apply {
            swipeRefreshLayout.setOnRefreshListener {
                viewModel.loadStatistics()
            }

            chipToday.setOnClickListener {
                viewModel.filterByPeriod(UsageStatisticsViewModel.Period.TODAY)
            }

            chipWeek.setOnClickListener {
                viewModel.filterByPeriod(UsageStatisticsViewModel.Period.WEEK)
            }

            chipMonth.setOnClickListener {
                viewModel.filterByPeriod(UsageStatisticsViewModel.Period.MONTH)
            }

            chipAll.setOnClickListener {
                viewModel.filterByPeriod(UsageStatisticsViewModel.Period.ALL)
            }
        }
    }

    private fun observeViewModel() {
        viewModel.usageRecords.observe(this) { records ->
            adapter.submitList(records)
            binding.textViewEmpty.visibility = if (records.isEmpty()) View.VISIBLE else View.GONE
        }

        viewModel.totalStats.observe(this) { stats ->
            binding.apply {
                textViewTotalUsers.text = stats.totalUsers.toString()
                textViewActiveUsers.text = stats.activeUsers.toString()
                textViewTotalData.text = String.format("%.2f GB", stats.totalDataUsed)
                textViewTotalSessions.text = stats.totalSessions.toString()
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.swipeRefreshLayout.isRefreshing = isLoading
            binding.progressBar.visibility = if (isLoading && adapter.itemCount == 0) View.VISIBLE else View.GONE
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }

        viewModel.selectedPeriod.observe(this) { period ->
            // Update chip selection
            binding.apply {
                chipToday.isChecked = period == UsageStatisticsViewModel.Period.TODAY
                chipWeek.isChecked = period == UsageStatisticsViewModel.Period.WEEK
                chipMonth.isChecked = period == UsageStatisticsViewModel.Period.MONTH
                chipAll.isChecked = period == UsageStatisticsViewModel.Period.ALL
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
