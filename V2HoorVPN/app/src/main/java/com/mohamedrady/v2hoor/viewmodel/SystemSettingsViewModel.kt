package com.mohamedrady.v2hoor.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.handler.FirebaseManager
import com.mohamedrady.v2hoor.util.SettingsManager
import kotlinx.coroutines.launch

class SystemSettingsViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsManager = SettingsManager(application)

    private val _settings = MutableLiveData<SystemSettings>()
    val settings: LiveData<SystemSettings> = _settings

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage

    data class SystemSettings(
        val defaultDataQuotaGB: Double = 10.0,
        val defaultSubscriptionDays: Int = 30,
        val autoServerSelection: Boolean = true,
        val maintenanceMode: Boolean = false,
        val totalUsers: Int = 0,
        val totalServers: Int = 0,
        val totalUsageRecords: Int = 0
    )

    fun loadSettings() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                // Load settings from SharedPreferences
                val currentSettings = SystemSettings(
                    defaultDataQuotaGB = settingsManager.getDefaultDataQuota(),
                    defaultSubscriptionDays = settingsManager.getDefaultSubscriptionDays(),
                    autoServerSelection = settingsManager.getAutoServerSelection(),
                    maintenanceMode = settingsManager.getMaintenanceMode()
                )

                // Load statistics from Firebase
                loadStatistics(currentSettings)
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load settings"
                _isLoading.value = false
            }
        }
    }

    private suspend fun loadStatistics(currentSettings: SystemSettings) {
        try {
            // Get total users
            val usersResult = FirebaseManager.getAllUsers()
            val totalUsers = usersResult.getOrNull()?.size ?: 0

            // Get total servers
            val serversResult = FirebaseManager.getAllServers()
            val totalServers = serversResult.getOrNull()?.size ?: 0

            // Get total usage records
            val usageResult = FirebaseManager.getAllUsageRecords()
            val totalUsageRecords = usageResult.getOrNull()?.size ?: 0

            _settings.value = currentSettings.copy(
                totalUsers = totalUsers,
                totalServers = totalServers,
                totalUsageRecords = totalUsageRecords
            )
        } catch (e: Exception) {
            _errorMessage.value = e.message ?: "Failed to load statistics"
        } finally {
            _isLoading.value = false
        }
    }

    fun updateDefaultDataQuota(quotaGB: Double) {
        viewModelScope.launch {
            try {
                settingsManager.setDefaultDataQuota(quotaGB)
                _successMessage.value = "Default data quota updated successfully"
                loadSettings() // Refresh settings
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update data quota"
            }
        }
    }

    fun updateDefaultSubscriptionDays(days: Int) {
        viewModelScope.launch {
            try {
                settingsManager.setDefaultSubscriptionDays(days)
                _successMessage.value = "Default subscription duration updated successfully"
                loadSettings() // Refresh settings
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update subscription duration"
            }
        }
    }

    fun updateAutoServerSelection(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setAutoServerSelection(enabled)
                _successMessage.value = "Auto server selection updated successfully"
                loadSettings() // Refresh settings
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update auto server selection"
            }
        }
    }

    fun updateMaintenanceMode(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setMaintenanceMode(enabled)
                _successMessage.value = "Maintenance mode updated successfully"
                loadSettings() // Refresh settings
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update maintenance mode"
            }
        }
    }

    fun clearUsageData() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.clearAllUsageData()
                result.onSuccess {
                    _successMessage.value = "Usage data cleared successfully"
                    loadSettings() // Refresh settings
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to clear usage data"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to clear usage data"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun exportData() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                // TODO: Implement data export functionality
                _successMessage.value = "Data export feature coming soon"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to export data"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun resetAllSettings() {
        viewModelScope.launch {
            try {
                settingsManager.resetToDefaults()
                _successMessage.value = "All settings reset to defaults"
                loadSettings() // Refresh settings
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to reset settings"
            }
        }
    }
}
