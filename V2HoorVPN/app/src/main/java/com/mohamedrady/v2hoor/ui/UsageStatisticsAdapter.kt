package com.mohamedrady.v2hoor.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemUsageStatisticsBinding
import com.mohamedrady.v2hoor.dto.UsageRecord
import java.text.SimpleDateFormat
import java.util.*

class UsageStatisticsAdapter : ListAdapter<UsageRecord, UsageStatisticsAdapter.UsageViewHolder>(UsageDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UsageViewHolder {
        val binding = ItemUsageStatisticsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return UsageViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UsageViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class UsageViewHolder(
        private val binding: ItemUsageStatisticsBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(usageRecord: UsageRecord) {
            binding.apply {
                // Session ID (shortened)
                textViewSessionId.text = usageRecord.sessionId.take(8)
                
                // User ID (shortened)
                textViewUserId.text = usageRecord.userId.take(8)
                
                // Data usage
                val totalDataMB = usageRecord.getTotalDataMB()
                textViewDataUsage.text = when {
                    totalDataMB >= 1024 -> String.format("%.2f GB", totalDataMB / 1024)
                    totalDataMB >= 1 -> String.format("%.1f MB", totalDataMB)
                    else -> String.format("%.0f KB", totalDataMB * 1024)
                }
                
                // Upload/Download breakdown
                val uploadMB = usageRecord.bytesUploaded / (1024.0 * 1024.0)
                val downloadMB = usageRecord.bytesDownloaded / (1024.0 * 1024.0)
                textViewUpload.text = String.format("↑ %.1f MB", uploadMB)
                textViewDownload.text = String.format("↓ %.1f MB", downloadMB)
                
                // Duration
                textViewDuration.text = usageRecord.getFormattedDuration()
                
                // Date and time
                textViewDateTime.text = usageRecord.createdAt?.let { 
                    SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(it)
                } ?: root.context.getString(R.string.unknown_date)
                
                // Connection status
                val statusText = if (usageRecord.isActiveSession()) {
                    root.context.getString(R.string.active_session)
                } else {
                    root.context.getString(R.string.completed_session)
                }
                textViewStatus.text = statusText
                
                // Status color
                val statusColor = if (usageRecord.isActiveSession()) {
                    R.color.success
                } else {
                    R.color.info
                }
                textViewStatus.setTextColor(root.context.getColor(statusColor))
                
                // Device info
                textViewDeviceInfo.text = usageRecord.deviceInfo.ifEmpty { 
                    root.context.getString(R.string.unknown_device) 
                }
                
                // App version
                textViewAppVersion.text = usageRecord.appVersion.ifEmpty { 
                    root.context.getString(R.string.unknown_version) 
                }
            }
        }
    }

    class UsageDiffCallback : DiffUtil.ItemCallback<UsageRecord>() {
        override fun areItemsTheSame(oldItem: UsageRecord, newItem: UsageRecord): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: UsageRecord, newItem: UsageRecord): Boolean {
            return oldItem == newItem
        }
    }
}
