package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.UsageRecord
import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.launch
import java.util.*

class UsageStatisticsViewModel : ViewModel() {

    private val _usageRecords = MutableLiveData<List<UsageRecord>>()
    val usageRecords: LiveData<List<UsageRecord>> = _usageRecords

    private val _totalStats = MutableLiveData<TotalStats>()
    val totalStats: LiveData<TotalStats> = _totalStats

    private val _selectedPeriod = MutableLiveData<Period>()
    val selectedPeriod: LiveData<Period> = _selectedPeriod

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private var allRecords: List<UsageRecord> = emptyList()

    enum class Period {
        TODAY, WEEK, MONTH, ALL
    }

    data class TotalStats(
        val totalUsers: Int = 0,
        val activeUsers: Int = 0,
        val totalDataUsed: Double = 0.0,
        val totalSessions: Int = 0
    )

    init {
        _selectedPeriod.value = Period.ALL
    }

    fun loadStatistics() {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                // Load all usage records
                val usageResult = FirebaseManager.getAllUsageRecords()
                usageResult.onSuccess { records ->
                    allRecords = records
                    filterByCurrentPeriod()
                    calculateTotalStats()
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load statistics"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load statistics"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun filterByPeriod(period: Period) {
        _selectedPeriod.value = period
        filterByCurrentPeriod()
    }

    private fun filterByCurrentPeriod() {
        val period = _selectedPeriod.value ?: Period.ALL
        val calendar = Calendar.getInstance()
        val now = calendar.time

        val filteredRecords = when (period) {
            Period.TODAY -> {
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                val startOfDay = calendar.time
                
                allRecords.filter { record ->
                    record.createdAt?.after(startOfDay) == true
                }
            }
            Period.WEEK -> {
                calendar.add(Calendar.DAY_OF_YEAR, -7)
                val weekAgo = calendar.time
                
                allRecords.filter { record ->
                    record.createdAt?.after(weekAgo) == true
                }
            }
            Period.MONTH -> {
                calendar.add(Calendar.MONTH, -1)
                val monthAgo = calendar.time
                
                allRecords.filter { record ->
                    record.createdAt?.after(monthAgo) == true
                }
            }
            Period.ALL -> allRecords
        }

        _usageRecords.value = filteredRecords.sortedByDescending { it.createdAt }
    }

    private fun calculateTotalStats() {
        viewModelScope.launch {
            try {
                // Get all users
                val usersResult = FirebaseManager.getAllUsers()
                usersResult.onSuccess { users ->
                    val activeUsers = users.count { it.isActive && it.canConnect() }
                    val totalDataUsed = allRecords.sumOf { it.getTotalDataGB() }
                    val totalSessions = allRecords.size

                    _totalStats.value = TotalStats(
                        totalUsers = users.size,
                        activeUsers = activeUsers,
                        totalDataUsed = totalDataUsed,
                        totalSessions = totalSessions
                    )
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to calculate statistics"
            }
        }
    }
}
