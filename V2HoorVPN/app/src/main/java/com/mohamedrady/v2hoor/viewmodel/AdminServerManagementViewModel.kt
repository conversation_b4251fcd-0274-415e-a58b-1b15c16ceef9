package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.VpnServer
import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.launch

class AdminServerManagementViewModel : ViewModel() {

    private val _servers = MutableLiveData<List<VpnServer>>()
    val servers: LiveData<List<VpnServer>> = _servers

    private val _selectedServer = MutableLiveData<VpnServer?>()
    val selectedServer: LiveData<VpnServer?> = _selectedServer

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage

    fun loadServers() {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.getAllServers()
                result.onSuccess { serversList ->
                    _servers.value = serversList.sortedByDescending { it.priority }
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load servers"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load servers"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun selectServer(server: VpnServer) {
        _selectedServer.value = server
    }

    fun toggleServerStatus(server: VpnServer) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.saveServer(server)
                result.onSuccess {
                    _successMessage.value = if (server.isActive) {
                        "Server activated successfully"
                    } else {
                        "Server deactivated successfully"
                    }
                    loadServers() // Refresh the list
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to update server status"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update server status"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun addServer(server: VpnServer) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.saveServer(server)
                result.onSuccess {
                    _successMessage.value = "Server added successfully"
                    loadServers() // Refresh the list
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to add server"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to add server"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun updateServer(server: VpnServer) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.saveServer(server)
                result.onSuccess {
                    _successMessage.value = "Server updated successfully"
                    loadServers() // Refresh the list
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to update server"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update server"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteServer(serverId: String) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.deleteServer(serverId)
                result.onSuccess {
                    _successMessage.value = "Server deleted successfully"
                    loadServers() // Refresh the list
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to delete server"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to delete server"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun testServerConnection(server: VpnServer) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                // TODO: Implement server connection testing
                _successMessage.value = "Server connection test feature coming soon"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to test server connection"
            } finally {
                _isLoading.value = false
            }
        }
    }
}
