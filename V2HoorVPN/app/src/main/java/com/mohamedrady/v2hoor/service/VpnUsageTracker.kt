package com.mohamedrady.v2hoor.service

import android.content.Context
import android.net.TrafficStats
import android.os.Build
import com.mohamedrady.v2hoor.dto.UsageRecord
import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.*
import java.util.*

/**
 * VPN Usage Tracker for monitoring data usage and connection statistics
 */
class VpnUsageTracker(private val context: Context) {
    
    private var isTracking = false
    private var trackingJob: Job? = null
    private var sessionStartTime: Date? = null
    private var initialRxBytes: Long = 0
    private var initialTxBytes: Long = 0
    private var currentSession: UsageRecord? = null
    
    // Callbacks for real-time updates
    var onStatsUpdate: ((uploadSpeed: Double, downloadSpeed: Double, totalData: Long) -> Unit)? = null
    
    private var lastRxBytes: Long = 0
    private var lastTxBytes: Long = 0
    private var lastUpdateTime: Long = 0
    
    companion object {
        private const val UPDATE_INTERVAL_MS = 1000L // Update every second
        private const val SAVE_INTERVAL_MS = 30000L // Save to Firebase every 30 seconds
    }
    
    /**
     * Start tracking VPN usage
     */
    fun startTracking(userId: String, serverId: String, serverName: String) {
        if (isTracking) return
        
        isTracking = true
        sessionStartTime = Date()
        
        // Get initial traffic stats
        initialRxBytes = getReceivedBytes()
        initialTxBytes = getTransmittedBytes()
        lastRxBytes = initialRxBytes
        lastTxBytes = initialTxBytes
        lastUpdateTime = System.currentTimeMillis()
        
        // Create session record
        currentSession = UsageRecord(
            userId = userId,
            serverId = serverId,
            sessionId = UUID.randomUUID().toString(),
            connectionStartTime = sessionStartTime,
            deviceInfo = getDeviceInfo(),
            appVersion = getAppVersion()
        )
        
        // Start tracking job
        trackingJob = CoroutineScope(Dispatchers.IO).launch {
            var lastSaveTime = System.currentTimeMillis()
            
            while (isTracking) {
                try {
                    updateStats()
                    
                    // Save to Firebase periodically
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastSaveTime >= SAVE_INTERVAL_MS) {
                        saveCurrentSession()
                        lastSaveTime = currentTime
                    }
                    
                    delay(UPDATE_INTERVAL_MS)
                } catch (e: Exception) {
                    // Log error but continue tracking
                    e.printStackTrace()
                }
            }
        }
    }
    
    /**
     * Stop tracking and save final session
     */
    suspend fun stopTracking() {
        if (!isTracking) return
        
        isTracking = false
        trackingJob?.cancel()
        
        // Save final session
        saveCurrentSession(isFinal = true)
        
        // Reset tracking data
        currentSession = null
        sessionStartTime = null
    }
    
    /**
     * Update real-time statistics
     */
    private suspend fun updateStats() {
        val currentTime = System.currentTimeMillis()
        val currentRxBytes = getReceivedBytes()
        val currentTxBytes = getTransmittedBytes()
        
        if (lastUpdateTime > 0) {
            val timeDiff = (currentTime - lastUpdateTime) / 1000.0 // seconds
            val rxDiff = currentRxBytes - lastRxBytes
            val txDiff = currentTxBytes - lastTxBytes
            
            if (timeDiff > 0) {
                val downloadSpeed = (rxDiff / timeDiff) / 1024.0 // KB/s
                val uploadSpeed = (txDiff / timeDiff) / 1024.0 // KB/s
                val totalData = (currentRxBytes - initialRxBytes) + (currentTxBytes - initialTxBytes)
                
                // Update UI on main thread
                withContext(Dispatchers.Main) {
                    onStatsUpdate?.invoke(uploadSpeed, downloadSpeed, totalData)
                }
            }
        }
        
        lastRxBytes = currentRxBytes
        lastTxBytes = currentTxBytes
        lastUpdateTime = currentTime
    }
    
    /**
     * Save current session to Firebase
     */
    private suspend fun saveCurrentSession(isFinal: Boolean = false) {
        val session = currentSession ?: return
        val currentTime = Date()
        
        val currentRxBytes = getReceivedBytes()
        val currentTxBytes = getTransmittedBytes()
        
        val totalRxBytes = currentRxBytes - initialRxBytes
        val totalTxBytes = currentTxBytes - initialTxBytes
        
        val duration = sessionStartTime?.let { 
            (currentTime.time - it.time) / 1000 
        } ?: 0L
        
        val updatedSession = session.copy(
            bytesDownloaded = totalRxBytes,
            bytesUploaded = totalTxBytes,
            durationSeconds = duration,
            connectionEndTime = if (isFinal) currentTime else null
        )
        
        try {
            FirebaseManager.recordUsage(updatedSession)
            currentSession = updatedSession
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Get received bytes from system
     */
    private fun getReceivedBytes(): Long {
        return try {
            TrafficStats.getTotalRxBytes()
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * Get transmitted bytes from system
     */
    private fun getTransmittedBytes(): Long {
        return try {
            TrafficStats.getTotalTxBytes()
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * Get device information
     */
    private fun getDeviceInfo(): String {
        return try {
            "${Build.MANUFACTURER} ${Build.MODEL} (Android ${Build.VERSION.RELEASE})"
        } catch (e: Exception) {
            "Unknown Device"
        }
    }
    
    /**
     * Get app version
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }
    }
    
    /**
     * Get current session statistics
     */
    fun getCurrentSessionStats(): SessionStats? {
        val session = currentSession ?: return null
        val startTime = sessionStartTime ?: return null
        
        val currentTime = Date()
        val duration = (currentTime.time - startTime.time) / 1000
        
        val currentRxBytes = getReceivedBytes()
        val currentTxBytes = getTransmittedBytes()
        
        val totalRxBytes = currentRxBytes - initialRxBytes
        val totalTxBytes = currentTxBytes - initialTxBytes
        val totalBytes = totalRxBytes + totalTxBytes
        
        return SessionStats(
            sessionId = session.sessionId,
            duration = duration,
            totalBytes = totalBytes,
            downloadedBytes = totalRxBytes,
            uploadedBytes = totalTxBytes,
            isActive = isTracking
        )
    }
    
    data class SessionStats(
        val sessionId: String,
        val duration: Long, // seconds
        val totalBytes: Long,
        val downloadedBytes: Long,
        val uploadedBytes: Long,
        val isActive: Boolean
    ) {
        fun getTotalMB(): Double = totalBytes / (1024.0 * 1024.0)
        fun getTotalGB(): Double = totalBytes / (1024.0 * 1024.0 * 1024.0)
        
        fun getFormattedDuration(): String {
            val hours = duration / 3600
            val minutes = (duration % 3600) / 60
            val seconds = duration % 60
            return String.format("%02d:%02d:%02d", hours, minutes, seconds)
        }
    }
}
