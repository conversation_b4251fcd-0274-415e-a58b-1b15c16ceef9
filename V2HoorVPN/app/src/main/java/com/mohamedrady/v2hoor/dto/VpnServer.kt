package com.mohamedrady.v2hoor.dto

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp
import java.util.Date

/**
 * VPN Server data model for Firebase Firestore
 */
data class VpnServer(
    @DocumentId
    val id: String = "",
    val name: String = "",
    val country: String = "",
    val city: String = "",
    val serverType: String = "", // VMESS, VLESS, TROJAN, SHADOWSOCKS, etc.
    val serverConfig: String = "", // The actual server configuration (JSON or URI)
    val isActive: Boolean = true,
    val maxUsers: Int = 0, // Maximum number of users that can use this server (0 = unlimited)
    val currentUsers: Int = 0, // Current number of connected users
    val allowedUsers: List<String> = emptyList(), // List of user IDs allowed to use this server (empty = all users)
    val priority: Int = 0, // Server priority for auto-selection (higher = better)
    val speedMbps: Double = 0.0, // Server speed in Mbps
    val latencyMs: Int = 0, // Server latency in milliseconds
    val isReadOnly: Boolean = false, // If true, server cannot be edited (imported servers)
    @ServerTimestamp
    val createdAt: Date? = null,
    @ServerTimestamp
    val updatedAt: Date? = null,
    val lastCheckedAt: Date? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        name = "",
        country = "",
        city = "",
        serverType = "",
        serverConfig = "",
        isActive = true,
        maxUsers = 0,
        currentUsers = 0,
        allowedUsers = emptyList(),
        priority = 0,
        speedMbps = 0.0,
        latencyMs = 0,
        isReadOnly = false,
        createdAt = null,
        updatedAt = null,
        lastCheckedAt = null
    )

    /**
     * Check if server is available for new connections
     */
    fun isAvailable(): Boolean {
        return isActive && (maxUsers == 0 || currentUsers < maxUsers)
    }

    /**
     * Check if a specific user can use this server
     */
    fun canUserAccess(userId: String): Boolean {
        return isActive && (allowedUsers.isEmpty() || allowedUsers.contains(userId))
    }

    /**
     * Get server location display string
     */
    fun getLocationString(): String {
        return if (city.isNotEmpty() && country.isNotEmpty()) {
            "$city, $country"
        } else if (country.isNotEmpty()) {
            country
        } else {
            "Unknown Location"
        }
    }

    /**
     * Get server quality score based on speed and latency
     */
    fun getQualityScore(): Double {
        val speedScore = speedMbps / 100.0 // Normalize speed (100 Mbps = 1.0)
        val latencyScore = if (latencyMs > 0) 1.0 / (latencyMs / 100.0) else 0.0 // Lower latency = higher score
        return (speedScore + latencyScore) / 2.0
    }
}
