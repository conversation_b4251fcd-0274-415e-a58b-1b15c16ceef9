package com.mohamedrady.v2hoor.dto

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp
import java.util.Date

/**
 * User data model for Firebase Firestore
 */
data class User(
    @DocumentId
    val id: String = "",
    val username: String = "",
    val email: String = "",
    val password: String = "", // This will be handled by Firebase Auth, not stored in Firestore
    val isAdmin: Boolean = false,
    val isActive: Boolean = true,
    val dataQuotaGB: Double = 0.0, // Data quota in GB
    val usedDataGB: Double = 0.0, // Used data in GB
    val subscriptionExpiry: Date? = null,
    val allowedServers: List<String> = emptyList(), // List of server IDs this user can access
    @ServerTimestamp
    val createdAt: Date? = null,
    @ServerTimestamp
    val updatedAt: Date? = null,
    val lastLoginAt: Date? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        username = "",
        email = "",
        password = "",
        isAdmin = false,
        isActive = true,
        dataQuotaGB = 0.0,
        usedDataGB = 0.0,
        subscriptionExpiry = null,
        allowedServers = emptyList(),
        createdAt = null,
        updatedAt = null,
        lastLoginAt = null
    )

    /**
     * Check if user's subscription is expired
     */
    fun isSubscriptionExpired(): Boolean {
        return subscriptionExpiry?.let { it.before(Date()) } ?: false
    }

    /**
     * Check if user has exceeded data quota
     */
    fun isDataQuotaExceeded(): Boolean {
        return usedDataGB >= dataQuotaGB
    }

    /**
     * Get remaining data quota in GB
     */
    fun getRemainingDataGB(): Double {
        return maxOf(0.0, dataQuotaGB - usedDataGB)
    }

    /**
     * Get data usage percentage
     */
    fun getDataUsagePercentage(): Double {
        return if (dataQuotaGB > 0) {
            (usedDataGB / dataQuotaGB) * 100
        } else {
            0.0
        }
    }

    /**
     * Check if user can connect (not expired and has data quota)
     */
    fun canConnect(): Boolean {
        return isActive && !isSubscriptionExpired() && !isDataQuotaExceeded()
    }
}
