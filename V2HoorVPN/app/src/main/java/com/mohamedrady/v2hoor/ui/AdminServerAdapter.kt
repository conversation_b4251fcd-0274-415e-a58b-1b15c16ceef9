package com.mohamedrady.v2hoor.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemAdminServerBinding
import com.mohamedrady.v2hoor.dto.VpnServer

class AdminServerAdapter(
    private val onServerClick: (VpnServer) -> Unit,
    private val onToggleActive: (VpnServer) -> Unit
) : ListAdapter<VpnServer, AdminServerAdapter.ServerViewHolder>(ServerDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServerViewHolder {
        val binding = ItemAdminServerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ServerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ServerViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ServerViewHolder(
        private val binding: ItemAdminServerBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(server: VpnServer) {
            binding.apply {
                textViewServerName.text = server.name.ifEmpty { "Unnamed Server" }
                textViewServerType.text = server.serverType
                textViewLocation.text = server.getLocationString()
                
                // Server status
                val statusText = if (server.isActive) {
                    root.context.getString(R.string.active)
                } else {
                    root.context.getString(R.string.inactive)
                }
                textViewStatus.text = statusText
                
                // Status color
                val statusColor = if (server.isActive) {
                    R.color.success
                } else {
                    R.color.error
                }
                textViewStatus.setTextColor(root.context.getColor(statusColor))
                
                // Users count
                val usersText = if (server.maxUsers > 0) {
                    "${server.currentUsers} / ${server.maxUsers}"
                } else {
                    "${server.currentUsers} / ∞"
                }
                textViewUsers.text = usersText
                
                // Progress bar for users
                if (server.maxUsers > 0) {
                    val usagePercentage = ((server.currentUsers.toFloat() / server.maxUsers) * 100).toInt()
                    progressBarUsers.progress = usagePercentage
                    progressBarUsers.visibility = android.view.View.VISIBLE
                } else {
                    progressBarUsers.visibility = android.view.View.GONE
                }
                
                // Priority
                textViewPriority.text = server.priority.toString()
                
                // Speed and latency
                if (server.speedMbps > 0) {
                    textViewSpeed.text = "${server.speedMbps.toInt()} Mbps"
                    textViewSpeed.visibility = android.view.View.VISIBLE
                } else {
                    textViewSpeed.visibility = android.view.View.GONE
                }
                
                if (server.latencyMs > 0) {
                    textViewLatency.text = "${server.latencyMs} ms"
                    textViewLatency.visibility = android.view.View.VISIBLE
                } else {
                    textViewLatency.visibility = android.view.View.GONE
                }
                
                // Read-only indicator
                chipReadOnly.visibility = if (server.isReadOnly) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }
                
                // Allowed users count
                val allowedUsersText = if (server.allowedUsers.isEmpty()) {
                    root.context.getString(R.string.all_users)
                } else {
                    "${server.allowedUsers.size} ${root.context.getString(R.string.users)}"
                }
                textViewAllowedUsers.text = allowedUsersText
                
                // Toggle switch
                switchActive.isChecked = server.isActive
                switchActive.setOnCheckedChangeListener { _, isChecked ->
                    if (isChecked != server.isActive) {
                        onToggleActive(server.copy(isActive = isChecked))
                    }
                }
                
                // Click listener
                root.setOnClickListener {
                    onServerClick(server)
                }
            }
        }
    }

    class ServerDiffCallback : DiffUtil.ItemCallback<VpnServer>() {
        override fun areItemsTheSame(oldItem: VpnServer, newItem: VpnServer): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: VpnServer, newItem: VpnServer): Boolean {
            return oldItem == newItem
        }
    }
}
