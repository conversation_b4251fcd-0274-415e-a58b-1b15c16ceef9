package com.mohamedrady.v2hoor.ui

import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.FragmentMainConnectionBinding
import com.mohamedrady.v2hoor.dto.VpnServer
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.MainConnectionViewModel
import kotlinx.coroutines.launch

class MainConnectionFragment : Fragment() {
    
    private var _binding: FragmentMainConnectionBinding? = null
    private val binding get() = _binding!!
    private val viewModel: MainConnectionViewModel by viewModels()
    
    private lateinit var serverAdapter: ArrayAdapter<VpnServer>
    private var isConnected = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMainConnectionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        observeViewModel()
        
        // Load user data and servers
        viewModel.loadUserData()
        viewModel.loadAvailableServers()
    }

    private fun setupUI() {
        binding.apply {
            // Setup server dropdown
            serverAdapter = ArrayAdapter(
                requireContext(),
                R.layout.item_server_dropdown,
                mutableListOf()
            )
            serverAdapter.setDropDownViewResource(R.layout.item_server_dropdown_expanded)
            spinnerServerSelection.adapter = serverAdapter

            // Connection button click
            btnConnection.setOnClickListener {
                if (isConnected) {
                    viewModel.disconnect()
                } else {
                    val selectedServer = spinnerServerSelection.selectedItem as? VpnServer
                    if (selectedServer != null) {
                        viewModel.connect(selectedServer)
                    } else {
                        toastError(R.string.please_select_server)
                    }
                }
            }

            // Auto server selection
            switchAutoSelection.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setAutoServerSelection(isChecked)
                spinnerServerSelection.isEnabled = !isChecked
            }

            // Refresh servers
            btnRefreshServers.setOnClickListener {
                viewModel.loadAvailableServers()
            }
        }
    }

    private fun observeViewModel() {
        viewModel.userStatus.observe(viewLifecycleOwner) { status ->
            binding.apply {
                textViewUsername.text = status.username
                textViewSubscriptionStatus.text = when {
                    status.isExpired -> getString(R.string.subscription_expired)
                    status.isQuotaExceeded -> getString(R.string.quota_exceeded)
                    !status.isActive -> getString(R.string.account_inactive)
                    else -> getString(R.string.active)
                }
                
                // Status color
                val statusColor = when {
                    status.isExpired || status.isQuotaExceeded || !status.isActive -> R.color.error
                    else -> R.color.success
                }
                textViewSubscriptionStatus.setTextColor(requireContext().getColor(statusColor))
                
                // Data usage
                textViewDataUsage.text = getString(
                    R.string.data_usage_format,
                    status.usedDataGB,
                    status.totalDataGB
                )
                
                val usagePercentage = ((status.usedDataGB / status.totalDataGB) * 100).toInt()
                progressBarDataUsage.progress = usagePercentage
                
                // Remaining data
                val remainingGB = status.totalDataGB - status.usedDataGB
                textViewRemainingData.text = getString(R.string.remaining_data_format, remainingGB)
                
                // Expiry date
                textViewExpiryDate.text = status.expiryDate ?: getString(R.string.no_expiry)
                
                // Enable/disable connection based on status
                btnConnection.isEnabled = status.canConnect
            }
        }

        viewModel.availableServers.observe(viewLifecycleOwner) { servers ->
            serverAdapter.clear()
            serverAdapter.addAll(servers)
            serverAdapter.notifyDataSetChanged()
            
            // Auto-select best server if auto selection is enabled
            if (binding.switchAutoSelection.isChecked && servers.isNotEmpty()) {
                val bestServer = servers.maxByOrNull { it.getQualityScore() }
                bestServer?.let { server ->
                    val position = serverAdapter.getPosition(server)
                    binding.spinnerServerSelection.setSelection(position)
                }
            }
        }

        viewModel.connectionStatus.observe(viewLifecycleOwner) { status ->
            updateConnectionUI(status)
        }

        viewModel.connectionStats.observe(viewLifecycleOwner) { stats ->
            binding.apply {
                textViewUploadSpeed.text = getString(R.string.upload_speed_format, stats.uploadSpeed)
                textViewDownloadSpeed.text = getString(R.string.download_speed_format, stats.downloadSpeed)
                textViewConnectionDuration.text = stats.formattedDuration
                textViewSessionData.text = getString(R.string.session_data_format, stats.sessionDataMB)
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBarLoading.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }

        viewModel.successMessage.observe(viewLifecycleOwner) { successMessage ->
            successMessage?.let {
                toastSuccess(it)
            }
        }
    }

    private fun updateConnectionUI(status: MainConnectionViewModel.ConnectionStatus) {
        binding.apply {
            isConnected = status.isConnected
            
            // Update connection button
            btnConnection.text = if (isConnected) {
                getString(R.string.disconnect)
            } else {
                getString(R.string.connect)
            }
            
            // Update button color and animation
            val buttonColor = if (isConnected) R.color.error else R.color.success
            btnConnection.setBackgroundColor(requireContext().getColor(buttonColor))
            
            // Animate button
            if (status.isConnecting || status.isDisconnecting) {
                startConnectionAnimation()
            } else {
                stopConnectionAnimation()
            }
            
            // Update status text
            textViewConnectionStatus.text = when {
                status.isConnecting -> getString(R.string.connecting)
                status.isDisconnecting -> getString(R.string.disconnecting)
                status.isConnected -> getString(R.string.connected_to, status.serverName)
                else -> getString(R.string.disconnected)
            }
            
            // Show/hide connection stats
            layoutConnectionStats.visibility = if (isConnected) View.VISIBLE else View.GONE
            
            // Enable/disable server selection
            spinnerServerSelection.isEnabled = !isConnected && !switchAutoSelection.isChecked
        }
    }

    private fun startConnectionAnimation() {
        val animator = ObjectAnimator.ofFloat(binding.btnConnection, "alpha", 1f, 0.5f, 1f)
        animator.duration = 1000
        animator.repeatCount = ObjectAnimator.INFINITE
        animator.start()
    }

    private fun stopConnectionAnimation() {
        binding.btnConnection.clearAnimation()
        binding.btnConnection.alpha = 1f
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
