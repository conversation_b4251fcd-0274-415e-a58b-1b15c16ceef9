package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityUserManagementBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.UserManagementViewModel

class UserManagementActivity : BaseActivity() {
    
    private lateinit var binding: ActivityUserManagementBinding
    private val viewModel: UserManagementViewModel by viewModels()
    private lateinit var adapter: UserManagementAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUserManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupUI()
        observeViewModel()
        
        // Load users
        viewModel.loadUsers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.user_management)
        }
    }

    private fun setupRecyclerView() {
        adapter = UserManagementAdapter { user ->
            // Handle user item click (edit user)
            viewModel.selectUser(user)
        }
        
        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@UserManagementActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupUI() {
        binding.apply {
            fabAddUser.setOnClickListener {
                // TODO: Open add user dialog
                toast(R.string.feature_coming_soon)
            }

            swipeRefreshLayout.setOnRefreshListener {
                viewModel.loadUsers()
            }
        }
    }

    private fun observeViewModel() {
        viewModel.users.observe(this) { users ->
            adapter.submitList(users)
            binding.textViewEmpty.visibility = if (users.isEmpty()) View.VISIBLE else View.GONE
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.swipeRefreshLayout.isRefreshing = isLoading
            binding.progressBar.visibility = if (isLoading && adapter.itemCount == 0) View.VISIBLE else View.GONE
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }

        viewModel.successMessage.observe(this) { successMessage ->
            successMessage?.let {
                toastSuccess(it)
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
