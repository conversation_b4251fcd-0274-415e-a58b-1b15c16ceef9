package com.mohamedrady.v2hoor.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemUserManagementBinding
import com.mohamedrady.v2hoor.dto.User
import java.text.SimpleDateFormat
import java.util.*

class UserManagementAdapter(
    private val onUserClick: (User) -> Unit
) : ListAdapter<User, UserManagementAdapter.UserViewHolder>(UserDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val binding = ItemUserManagementBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return UserViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class UserViewHolder(
        private val binding: ItemUserManagementBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(user: User) {
            binding.apply {
                textViewUsername.text = user.username
                textViewEmail.text = user.email
                
                // Status
                val statusText = when {
                    !user.isActive -> root.context.getString(R.string.inactive)
                    user.isSubscriptionExpired() -> root.context.getString(R.string.expired)
                    user.isDataQuotaExceeded() -> root.context.getString(R.string.quota_exceeded)
                    else -> root.context.getString(R.string.active)
                }
                textViewStatus.text = statusText
                
                // Status color
                val statusColor = when {
                    !user.isActive -> R.color.error
                    user.isSubscriptionExpired() -> R.color.warning
                    user.isDataQuotaExceeded() -> R.color.warning
                    else -> R.color.success
                }
                textViewStatus.setTextColor(root.context.getColor(statusColor))
                
                // Data usage
                val usageText = "${String.format("%.2f", user.usedDataGB)} / ${String.format("%.2f", user.dataQuotaGB)} GB"
                textViewDataUsage.text = usageText
                
                // Progress bar
                val usagePercentage = user.getDataUsagePercentage().toInt()
                progressBarDataUsage.progress = usagePercentage
                
                // Expiry date
                textViewExpiry.text = user.subscriptionExpiry?.let { 
                    SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(it)
                } ?: root.context.getString(R.string.no_expiry)
                
                // Admin badge
                chipAdmin.visibility = if (user.isAdmin) android.view.View.VISIBLE else android.view.View.GONE
                
                root.setOnClickListener {
                    onUserClick(user)
                }
            }
        }
    }

    class UserDiffCallback : DiffUtil.ItemCallback<User>() {
        override fun areItemsTheSame(oldItem: User, newItem: User): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: User, newItem: User): Boolean {
            return oldItem == newItem
        }
    }
}
