package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.launch

class AdminPanelViewModel : ViewModel() {

    private val _isAdmin = MutableLiveData<Boolean>()
    val isAdmin: LiveData<Boolean> = _isAdmin

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    fun checkAdminAccess() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val currentUser = FirebaseManager.getCurrentUser()
                if (currentUser != null) {
                    val userResult = FirebaseManager.getUserData(currentUser.uid)
                    userResult.onSuccess { user ->
                        _isAdmin.value = user.isAdmin
                    }.onFailure { exception ->
                        _errorMessage.value = exception.message
                        _isAdmin.value = false
                    }
                } else {
                    _isAdmin.value = false
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message
                _isAdmin.value = false
            } finally {
                _isLoading.value = false
            }
        }
    }
}
