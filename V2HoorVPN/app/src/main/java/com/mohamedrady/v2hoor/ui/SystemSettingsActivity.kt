package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.activity.viewModels
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivitySystemSettingsBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.SystemSettingsViewModel

class SystemSettingsActivity : BaseActivity() {
    
    private lateinit var binding: ActivitySystemSettingsBinding
    private val viewModel: SystemSettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySystemSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        observeViewModel()
        
        // Load current settings
        viewModel.loadSettings()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.system_settings)
        }
    }

    private fun setupUI() {
        binding.apply {
            // Default data quota setting
            btnSaveQuota.setOnClickListener {
                val quota = etDefaultQuota.text.toString().toDoubleOrNull()
                if (quota != null && quota > 0) {
                    viewModel.updateDefaultDataQuota(quota)
                } else {
                    toastError(R.string.invalid_quota_value)
                }
            }

            // Default subscription duration setting
            btnSaveSubscription.setOnClickListener {
                val days = etDefaultSubscriptionDays.text.toString().toIntOrNull()
                if (days != null && days > 0) {
                    viewModel.updateDefaultSubscriptionDays(days)
                } else {
                    toastError(R.string.invalid_subscription_days)
                }
            }

            // Auto server selection setting
            switchAutoServerSelection.setOnCheckedChangeListener { _, isChecked ->
                viewModel.updateAutoServerSelection(isChecked)
            }

            // Maintenance mode setting
            switchMaintenanceMode.setOnCheckedChangeListener { _, isChecked ->
                viewModel.updateMaintenanceMode(isChecked)
            }

            // Clear usage data
            btnClearUsageData.setOnClickListener {
                showConfirmationDialog(
                    title = getString(R.string.clear_usage_data),
                    message = getString(R.string.clear_usage_data_confirmation),
                    onConfirm = {
                        viewModel.clearUsageData()
                    }
                )
            }

            // Export data
            btnExportData.setOnClickListener {
                viewModel.exportData()
            }

            // Reset all settings
            btnResetSettings.setOnClickListener {
                showConfirmationDialog(
                    title = getString(R.string.reset_settings),
                    message = getString(R.string.reset_settings_confirmation),
                    onConfirm = {
                        viewModel.resetAllSettings()
                    }
                )
            }
        }
    }

    private fun observeViewModel() {
        viewModel.settings.observe(this) { settings ->
            binding.apply {
                etDefaultQuota.setText(settings.defaultDataQuotaGB.toString())
                etDefaultSubscriptionDays.setText(settings.defaultSubscriptionDays.toString())
                switchAutoServerSelection.isChecked = settings.autoServerSelection
                switchMaintenanceMode.isChecked = settings.maintenanceMode
                
                textViewTotalUsers.text = settings.totalUsers.toString()
                textViewTotalServers.text = settings.totalServers.toString()
                textViewTotalUsageRecords.text = settings.totalUsageRecords.toString()
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }

        viewModel.successMessage.observe(this) { successMessage ->
            successMessage?.let {
                toastSuccess(it)
            }
        }
    }

    private fun showConfirmationDialog(title: String, message: String, onConfirm: () -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(R.string.confirm) { _, _ ->
                onConfirm()
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
