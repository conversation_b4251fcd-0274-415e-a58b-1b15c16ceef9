package com.mohamedrady.v2hoor.util

import android.content.Context
import android.content.SharedPreferences

class SettingsManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )

    companion object {
        private const val PREFS_NAME = "v2hoor_system_settings"
        
        // Setting keys
        private const val KEY_DEFAULT_DATA_QUOTA = "default_data_quota_gb"
        private const val KEY_DEFAULT_SUBSCRIPTION_DAYS = "default_subscription_days"
        private const val KEY_AUTO_SERVER_SELECTION = "auto_server_selection"
        private const val KEY_MAINTENANCE_MODE = "maintenance_mode"
        private const val KEY_MAX_CONCURRENT_CONNECTIONS = "max_concurrent_connections"
        private const val KEY_CONNECTION_TIMEOUT = "connection_timeout_seconds"
        private const val KEY_ENABLE_ANALYTICS = "enable_analytics"
        private const val KEY_ENABLE_CRASH_REPORTING = "enable_crash_reporting"
        
        // Default values
        private const val DEFAULT_DATA_QUOTA_GB = 10.0
        private const val DEFAULT_SUBSCRIPTION_DAYS = 30
        private const val DEFAULT_AUTO_SERVER_SELECTION = true
        private const val DEFAULT_MAINTENANCE_MODE = false
        private const val DEFAULT_MAX_CONCURRENT_CONNECTIONS = 100
        private const val DEFAULT_CONNECTION_TIMEOUT = 30
        private const val DEFAULT_ENABLE_ANALYTICS = true
        private const val DEFAULT_ENABLE_CRASH_REPORTING = true

        // Advanced settings keys
        private const val KEY_AUTO_CONNECT = "auto_connect"
        private const val KEY_KILL_SWITCH = "kill_switch"
        private const val KEY_CUSTOM_DNS = "custom_dns"
        private const val KEY_PRIMARY_DNS = "primary_dns"
        private const val KEY_SECONDARY_DNS = "secondary_dns"
        private const val KEY_PREFERRED_PROTOCOL = "preferred_protocol"
        private const val KEY_NOTIFICATIONS_ENABLED = "notifications_enabled"
        private const val KEY_QUOTA_ALERTS_ENABLED = "quota_alerts_enabled"
        private const val KEY_SUBSCRIPTION_ALERTS_ENABLED = "subscription_alerts_enabled"
        private const val KEY_DATA_SAVING_MODE = "data_saving_mode"
        private const val KEY_BACKGROUND_CONNECTION = "background_connection"

        // Advanced settings defaults
        private const val DEFAULT_AUTO_CONNECT = false
        private const val DEFAULT_KILL_SWITCH = true
        private const val DEFAULT_CUSTOM_DNS = false
        private const val DEFAULT_PRIMARY_DNS = "*******"
        private const val DEFAULT_SECONDARY_DNS = "*******"
        private const val DEFAULT_PREFERRED_PROTOCOL = "Auto"
        private const val DEFAULT_NOTIFICATIONS_ENABLED = true
        private const val DEFAULT_QUOTA_ALERTS_ENABLED = true
        private const val DEFAULT_SUBSCRIPTION_ALERTS_ENABLED = true
        private const val DEFAULT_DATA_SAVING_MODE = false
        private const val DEFAULT_BACKGROUND_CONNECTION = true
    }

    // Default Data Quota
    fun getDefaultDataQuota(): Double {
        return sharedPreferences.getFloat(KEY_DEFAULT_DATA_QUOTA, DEFAULT_DATA_QUOTA_GB.toFloat()).toDouble()
    }

    fun setDefaultDataQuota(quotaGB: Double) {
        sharedPreferences.edit()
            .putFloat(KEY_DEFAULT_DATA_QUOTA, quotaGB.toFloat())
            .apply()
    }

    // Default Subscription Days
    fun getDefaultSubscriptionDays(): Int {
        return sharedPreferences.getInt(KEY_DEFAULT_SUBSCRIPTION_DAYS, DEFAULT_SUBSCRIPTION_DAYS)
    }

    fun setDefaultSubscriptionDays(days: Int) {
        sharedPreferences.edit()
            .putInt(KEY_DEFAULT_SUBSCRIPTION_DAYS, days)
            .apply()
    }

    // Auto Server Selection
    fun getAutoServerSelection(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_SERVER_SELECTION, DEFAULT_AUTO_SERVER_SELECTION)
    }

    fun setAutoServerSelection(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_SERVER_SELECTION, enabled)
            .apply()
    }

    // Maintenance Mode
    fun getMaintenanceMode(): Boolean {
        return sharedPreferences.getBoolean(KEY_MAINTENANCE_MODE, DEFAULT_MAINTENANCE_MODE)
    }

    fun setMaintenanceMode(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_MAINTENANCE_MODE, enabled)
            .apply()
    }

    // Max Concurrent Connections
    fun getMaxConcurrentConnections(): Int {
        return sharedPreferences.getInt(KEY_MAX_CONCURRENT_CONNECTIONS, DEFAULT_MAX_CONCURRENT_CONNECTIONS)
    }

    fun setMaxConcurrentConnections(maxConnections: Int) {
        sharedPreferences.edit()
            .putInt(KEY_MAX_CONCURRENT_CONNECTIONS, maxConnections)
            .apply()
    }

    // Connection Timeout
    fun getConnectionTimeout(): Int {
        return sharedPreferences.getInt(KEY_CONNECTION_TIMEOUT, DEFAULT_CONNECTION_TIMEOUT)
    }

    fun setConnectionTimeout(timeoutSeconds: Int) {
        sharedPreferences.edit()
            .putInt(KEY_CONNECTION_TIMEOUT, timeoutSeconds)
            .apply()
    }

    // Analytics
    fun getEnableAnalytics(): Boolean {
        return sharedPreferences.getBoolean(KEY_ENABLE_ANALYTICS, DEFAULT_ENABLE_ANALYTICS)
    }

    fun setEnableAnalytics(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_ENABLE_ANALYTICS, enabled)
            .apply()
    }

    // Crash Reporting
    fun getEnableCrashReporting(): Boolean {
        return sharedPreferences.getBoolean(KEY_ENABLE_CRASH_REPORTING, DEFAULT_ENABLE_CRASH_REPORTING)
    }

    fun setEnableCrashReporting(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_ENABLE_CRASH_REPORTING, enabled)
            .apply()
    }

    // Reset all settings to defaults
    fun resetToDefaults() {
        sharedPreferences.edit()
            .putFloat(KEY_DEFAULT_DATA_QUOTA, DEFAULT_DATA_QUOTA_GB.toFloat())
            .putInt(KEY_DEFAULT_SUBSCRIPTION_DAYS, DEFAULT_SUBSCRIPTION_DAYS)
            .putBoolean(KEY_AUTO_SERVER_SELECTION, DEFAULT_AUTO_SERVER_SELECTION)
            .putBoolean(KEY_MAINTENANCE_MODE, DEFAULT_MAINTENANCE_MODE)
            .putInt(KEY_MAX_CONCURRENT_CONNECTIONS, DEFAULT_MAX_CONCURRENT_CONNECTIONS)
            .putInt(KEY_CONNECTION_TIMEOUT, DEFAULT_CONNECTION_TIMEOUT)
            .putBoolean(KEY_ENABLE_ANALYTICS, DEFAULT_ENABLE_ANALYTICS)
            .putBoolean(KEY_ENABLE_CRASH_REPORTING, DEFAULT_ENABLE_CRASH_REPORTING)
            .apply()
    }

    // Clear all settings
    fun clearAllSettings() {
        sharedPreferences.edit().clear().apply()
    }

    // Check if this is first run
    fun isFirstRun(): Boolean {
        val isFirstRun = sharedPreferences.getBoolean("is_first_run", true)
        if (isFirstRun) {
            sharedPreferences.edit()
                .putBoolean("is_first_run", false)
                .apply()
        }
        return isFirstRun
    }

    // Advanced Settings

    // Auto Connect
    fun getAutoConnect(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_CONNECT, DEFAULT_AUTO_CONNECT)
    }

    fun setAutoConnect(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_CONNECT, enabled)
            .apply()
    }

    // Kill Switch
    fun getKillSwitch(): Boolean {
        return sharedPreferences.getBoolean(KEY_KILL_SWITCH, DEFAULT_KILL_SWITCH)
    }

    fun setKillSwitch(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_KILL_SWITCH, enabled)
            .apply()
    }

    // Custom DNS
    fun getCustomDns(): Boolean {
        return sharedPreferences.getBoolean(KEY_CUSTOM_DNS, DEFAULT_CUSTOM_DNS)
    }

    fun setCustomDns(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_CUSTOM_DNS, enabled)
            .apply()
    }

    // Primary DNS
    fun getPrimaryDns(): String {
        return sharedPreferences.getString(KEY_PRIMARY_DNS, DEFAULT_PRIMARY_DNS) ?: DEFAULT_PRIMARY_DNS
    }

    fun setPrimaryDns(dns: String) {
        sharedPreferences.edit()
            .putString(KEY_PRIMARY_DNS, dns)
            .apply()
    }

    // Secondary DNS
    fun getSecondaryDns(): String {
        return sharedPreferences.getString(KEY_SECONDARY_DNS, DEFAULT_SECONDARY_DNS) ?: DEFAULT_SECONDARY_DNS
    }

    fun setSecondaryDns(dns: String) {
        sharedPreferences.edit()
            .putString(KEY_SECONDARY_DNS, dns)
            .apply()
    }

    // Preferred Protocol
    fun getPreferredProtocol(): String {
        return sharedPreferences.getString(KEY_PREFERRED_PROTOCOL, DEFAULT_PREFERRED_PROTOCOL) ?: DEFAULT_PREFERRED_PROTOCOL
    }

    fun setPreferredProtocol(protocol: String) {
        sharedPreferences.edit()
            .putString(KEY_PREFERRED_PROTOCOL, protocol)
            .apply()
    }

    // Notifications Enabled
    fun getNotificationsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_NOTIFICATIONS_ENABLED, DEFAULT_NOTIFICATIONS_ENABLED)
    }

    fun setNotificationsEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_NOTIFICATIONS_ENABLED, enabled)
            .apply()
    }

    // Quota Alerts Enabled
    fun getQuotaAlertsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_QUOTA_ALERTS_ENABLED, DEFAULT_QUOTA_ALERTS_ENABLED)
    }

    fun setQuotaAlertsEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_QUOTA_ALERTS_ENABLED, enabled)
            .apply()
    }

    // Subscription Alerts Enabled
    fun getSubscriptionAlertsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_SUBSCRIPTION_ALERTS_ENABLED, DEFAULT_SUBSCRIPTION_ALERTS_ENABLED)
    }

    fun setSubscriptionAlertsEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_SUBSCRIPTION_ALERTS_ENABLED, enabled)
            .apply()
    }

    // Data Saving Mode
    fun getDataSavingMode(): Boolean {
        return sharedPreferences.getBoolean(KEY_DATA_SAVING_MODE, DEFAULT_DATA_SAVING_MODE)
    }

    fun setDataSavingMode(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_DATA_SAVING_MODE, enabled)
            .apply()
    }

    // Background Connection
    fun getBackgroundConnection(): Boolean {
        return sharedPreferences.getBoolean(KEY_BACKGROUND_CONNECTION, DEFAULT_BACKGROUND_CONNECTION)
    }

    fun setBackgroundConnection(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_BACKGROUND_CONNECTION, enabled)
            .apply()
    }
}
