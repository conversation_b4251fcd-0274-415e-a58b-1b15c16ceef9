package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.activity.viewModels
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdvancedSettingsBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.AdvancedSettingsViewModel

class AdvancedSettingsActivity : BaseActivity() {
    
    private lateinit var binding: ActivityAdvancedSettingsBinding
    private val viewModel: AdvancedSettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAdvancedSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        observeViewModel()
        
        // Load current settings
        viewModel.loadSettings()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.advanced_settings)
        }
    }

    private fun setupUI() {
        binding.apply {
            // Auto-connect settings
            switchAutoConnect.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setAutoConnect(isChecked)
            }

            // Kill switch settings
            switchKillSwitch.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setKillSwitch(isChecked)
            }

            // DNS settings
            switchCustomDns.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setCustomDns(isChecked)
                layoutDnsServers.visibility = if (isChecked) android.view.View.VISIBLE else android.view.View.GONE
            }

            btnSaveDns.setOnClickListener {
                val primaryDns = etPrimaryDns.text.toString().trim()
                val secondaryDns = etSecondaryDns.text.toString().trim()
                viewModel.saveDnsSettings(primaryDns, secondaryDns)
            }

            // Connection timeout
            btnSaveTimeout.setOnClickListener {
                val timeout = etConnectionTimeout.text.toString().toIntOrNull()
                if (timeout != null && timeout > 0) {
                    viewModel.setConnectionTimeout(timeout)
                } else {
                    toastError(R.string.invalid_timeout_value)
                }
            }

            // Protocol settings
            spinnerProtocol.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                    val protocols = resources.getStringArray(R.array.vpn_protocols)
                    viewModel.setPreferredProtocol(protocols[position])
                }
                override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
            }

            // Notifications settings
            switchNotifications.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setNotificationsEnabled(isChecked)
            }

            switchQuotaAlerts.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setQuotaAlertsEnabled(isChecked)
            }

            switchSubscriptionAlerts.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setSubscriptionAlertsEnabled(isChecked)
            }

            // Data saving settings
            switchDataSaving.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setDataSavingMode(isChecked)
            }

            // Background connection
            switchBackgroundConnection.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setBackgroundConnection(isChecked)
            }

            // Reset settings
            btnResetAdvancedSettings.setOnClickListener {
                showConfirmationDialog(
                    title = getString(R.string.reset_advanced_settings),
                    message = getString(R.string.reset_advanced_settings_confirmation),
                    onConfirm = {
                        viewModel.resetAdvancedSettings()
                    }
                )
            }
        }
    }

    private fun observeViewModel() {
        viewModel.settings.observe(this) { settings ->
            binding.apply {
                switchAutoConnect.isChecked = settings.autoConnect
                switchKillSwitch.isChecked = settings.killSwitch
                switchCustomDns.isChecked = settings.customDns
                etPrimaryDns.setText(settings.primaryDns)
                etSecondaryDns.setText(settings.secondaryDns)
                etConnectionTimeout.setText(settings.connectionTimeout.toString())
                
                // Set protocol spinner
                val protocols = resources.getStringArray(R.array.vpn_protocols)
                val protocolIndex = protocols.indexOf(settings.preferredProtocol)
                if (protocolIndex >= 0) {
                    spinnerProtocol.setSelection(protocolIndex)
                }
                
                switchNotifications.isChecked = settings.notificationsEnabled
                switchQuotaAlerts.isChecked = settings.quotaAlertsEnabled
                switchSubscriptionAlerts.isChecked = settings.subscriptionAlertsEnabled
                switchDataSaving.isChecked = settings.dataSavingMode
                switchBackgroundConnection.isChecked = settings.backgroundConnection
                
                // Show/hide DNS layout
                layoutDnsServers.visibility = if (settings.customDns) android.view.View.VISIBLE else android.view.View.GONE
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }

        viewModel.successMessage.observe(this) { successMessage ->
            successMessage?.let {
                toastSuccess(it)
            }
        }
    }

    private fun showConfirmationDialog(title: String, message: String, onConfirm: () -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(R.string.confirm) { _, _ ->
                onConfirm()
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
