package com.mohamedrady.v2hoor.service

import android.content.Context
import com.mohamedrady.v2hoor.dto.User
import com.mohamedrady.v2hoor.handler.FirebaseManager
import com.mohamedrady.v2hoor.util.V2HoorNotificationManager
import kotlinx.coroutines.*
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * User Status Monitor for checking subscription and quota status
 */
class UserStatusMonitor(private val context: Context) {
    
    private val notificationManager = V2HoorNotificationManager(context)
    private var monitoringJob: Job? = null
    private var isMonitoring = false
    
    // Thresholds for warnings
    private val quotaWarningThresholds = listOf(80, 90, 95) // Percentages
    private val subscriptionWarningDays = listOf(7, 3, 1) // Days before expiry
    
    // Track last warning states to avoid spam
    private var lastQuotaWarningLevel = 0
    private var lastSubscriptionWarningDays = 0
    private var hasShownQuotaExceeded = false
    private var hasShownSubscriptionExpired = false
    
    companion object {
        private const val CHECK_INTERVAL_MINUTES = 15L // Check every 15 minutes
    }
    
    /**
     * Start monitoring user status
     */
    fun startMonitoring() {
        if (isMonitoring) return
        
        isMonitoring = true
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            while (isMonitoring) {
                try {
                    checkUserStatus()
                    delay(TimeUnit.MINUTES.toMillis(CHECK_INTERVAL_MINUTES))
                } catch (e: Exception) {
                    e.printStackTrace()
                    delay(TimeUnit.MINUTES.toMillis(5)) // Retry after 5 minutes on error
                }
            }
        }
    }
    
    /**
     * Stop monitoring
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
    }
    
    /**
     * Check user status and send notifications if needed
     */
    private suspend fun checkUserStatus() {
        try {
            val currentUser = FirebaseManager.getCurrentUser() ?: return
            val userResult = FirebaseManager.getUserData(currentUser.uid)
            
            userResult.onSuccess { user ->
                checkDataQuotaStatus(user)
                checkSubscriptionStatus(user)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Check data quota status and send warnings
     */
    private fun checkDataQuotaStatus(user: User) {
        val usagePercentage = user.getDataUsagePercentage()
        val remainingGB = user.getRemainingDataGB()
        
        when {
            user.isDataQuotaExceeded() -> {
                if (!hasShownQuotaExceeded) {
                    notificationManager.showQuotaExceededNotification()
                    hasShownQuotaExceeded = true
                }
            }
            
            usagePercentage >= 95 && lastQuotaWarningLevel < 95 -> {
                notificationManager.showQuotaWarningNotification(95, remainingGB)
                lastQuotaWarningLevel = 95
                hasShownQuotaExceeded = false
            }
            
            usagePercentage >= 90 && lastQuotaWarningLevel < 90 -> {
                notificationManager.showQuotaWarningNotification(90, remainingGB)
                lastQuotaWarningLevel = 90
                hasShownQuotaExceeded = false
            }
            
            usagePercentage >= 80 && lastQuotaWarningLevel < 80 -> {
                notificationManager.showQuotaWarningNotification(80, remainingGB)
                lastQuotaWarningLevel = 80
                hasShownQuotaExceeded = false
            }
            
            usagePercentage < 80 -> {
                // Reset warning level if usage drops below 80%
                lastQuotaWarningLevel = 0
                hasShownQuotaExceeded = false
            }
        }
    }
    
    /**
     * Check subscription status and send warnings
     */
    private fun checkSubscriptionStatus(user: User) {
        val expiryDate = user.subscriptionExpiry
        if (expiryDate == null) return // No expiry date set
        
        val currentDate = Date()
        val daysUntilExpiry = getDaysUntilExpiry(currentDate, expiryDate)
        
        when {
            user.isSubscriptionExpired() -> {
                if (!hasShownSubscriptionExpired) {
                    notificationManager.showSubscriptionExpiredNotification()
                    hasShownSubscriptionExpired = true
                }
            }
            
            daysUntilExpiry <= 1 && lastSubscriptionWarningDays != 1 -> {
                notificationManager.showSubscriptionExpiringNotification(1)
                lastSubscriptionWarningDays = 1
                hasShownSubscriptionExpired = false
            }
            
            daysUntilExpiry <= 3 && lastSubscriptionWarningDays != 3 -> {
                notificationManager.showSubscriptionExpiringNotification(3)
                lastSubscriptionWarningDays = 3
                hasShownSubscriptionExpired = false
            }
            
            daysUntilExpiry <= 7 && lastSubscriptionWarningDays != 7 -> {
                notificationManager.showSubscriptionExpiringNotification(7)
                lastSubscriptionWarningDays = 7
                hasShownSubscriptionExpired = false
            }
            
            daysUntilExpiry > 7 -> {
                // Reset warning state if subscription is renewed
                lastSubscriptionWarningDays = 0
                hasShownSubscriptionExpired = false
            }
        }
    }
    
    /**
     * Calculate days until expiry
     */
    private fun getDaysUntilExpiry(currentDate: Date, expiryDate: Date): Int {
        val diffInMillis = expiryDate.time - currentDate.time
        return (diffInMillis / (1000 * 60 * 60 * 24)).toInt()
    }
    
    /**
     * Force check user status immediately
     */
    suspend fun forceCheck() {
        checkUserStatus()
    }
    
    /**
     * Reset warning states (useful when user data is updated)
     */
    fun resetWarningStates() {
        lastQuotaWarningLevel = 0
        lastSubscriptionWarningDays = 0
        hasShownQuotaExceeded = false
        hasShownSubscriptionExpired = false
    }
    
    /**
     * Check if user can connect based on current status
     */
    suspend fun canUserConnect(): Boolean {
        return try {
            val currentUser = FirebaseManager.getCurrentUser() ?: return false
            val userResult = FirebaseManager.getUserData(currentUser.uid)
            
            userResult.getOrNull()?.canConnect() ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get current user status summary
     */
    suspend fun getUserStatusSummary(): UserStatusSummary? {
        return try {
            val currentUser = FirebaseManager.getCurrentUser() ?: return null
            val userResult = FirebaseManager.getUserData(currentUser.uid)
            
            userResult.getOrNull()?.let { user ->
                UserStatusSummary(
                    username = user.username,
                    isActive = user.isActive,
                    canConnect = user.canConnect(),
                    dataUsagePercentage = user.getDataUsagePercentage(),
                    remainingDataGB = user.getRemainingDataGB(),
                    isSubscriptionExpired = user.isSubscriptionExpired(),
                    daysUntilExpiry = user.subscriptionExpiry?.let { 
                        getDaysUntilExpiry(Date(), it) 
                    }
                )
            }
        } catch (e: Exception) {
            null
        }
    }
    
    data class UserStatusSummary(
        val username: String,
        val isActive: Boolean,
        val canConnect: Boolean,
        val dataUsagePercentage: Double,
        val remainingDataGB: Double,
        val isSubscriptionExpired: Boolean,
        val daysUntilExpiry: Int?
    )
}
