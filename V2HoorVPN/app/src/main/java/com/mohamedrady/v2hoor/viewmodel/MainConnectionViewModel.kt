package com.mohamedrady.v2hoor.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.UsageRecord
import com.mohamedrady.v2hoor.dto.User
import com.mohamedrady.v2hoor.dto.VpnServer
import com.mohamedrady.v2hoor.handler.FirebaseManager
import com.mohamedrady.v2hoor.util.SettingsManager
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class MainConnectionViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsManager = SettingsManager(application)

    private val _userStatus = MutableLiveData<UserStatus>()
    val userStatus: LiveData<UserStatus> = _userStatus

    private val _availableServers = MutableLiveData<List<VpnServer>>()
    val availableServers: LiveData<List<VpnServer>> = _availableServers

    private val _connectionStatus = MutableLiveData<ConnectionStatus>()
    val connectionStatus: LiveData<ConnectionStatus> = _connectionStatus

    private val _connectionStats = MutableLiveData<ConnectionStats>()
    val connectionStats: LiveData<ConnectionStats> = _connectionStats

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage

    private var currentSession: UsageRecord? = null
    private var statsUpdateJob: Job? = null
    private var sessionStartTime: Date? = null

    data class UserStatus(
        val username: String = "",
        val isActive: Boolean = false,
        val isExpired: Boolean = false,
        val isQuotaExceeded: Boolean = false,
        val usedDataGB: Double = 0.0,
        val totalDataGB: Double = 0.0,
        val expiryDate: String? = null,
        val canConnect: Boolean = false
    )

    data class ConnectionStatus(
        val isConnected: Boolean = false,
        val isConnecting: Boolean = false,
        val isDisconnecting: Boolean = false,
        val serverName: String = "",
        val serverId: String = ""
    )

    data class ConnectionStats(
        val uploadSpeed: Double = 0.0, // KB/s
        val downloadSpeed: Double = 0.0, // KB/s
        val sessionDataMB: Double = 0.0,
        val formattedDuration: String = "00:00:00"
    )

    init {
        _connectionStatus.value = ConnectionStatus()
        _connectionStats.value = ConnectionStats()
    }

    fun loadUserData() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val currentUser = FirebaseManager.getCurrentUser()
                if (currentUser != null) {
                    val userResult = FirebaseManager.getUserData(currentUser.uid)
                    userResult.onSuccess { user ->
                        val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
                        val expiryDateString = user.subscriptionExpiry?.let { dateFormat.format(it) }
                        
                        _userStatus.value = UserStatus(
                            username = user.username,
                            isActive = user.isActive,
                            isExpired = user.isSubscriptionExpired(),
                            isQuotaExceeded = user.isDataQuotaExceeded(),
                            usedDataGB = user.usedDataGB,
                            totalDataGB = user.dataQuotaGB,
                            expiryDate = expiryDateString,
                            canConnect = user.canConnect()
                        )
                    }.onFailure { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load user data"
                    }
                } else {
                    _errorMessage.value = "User not logged in"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load user data"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadAvailableServers() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val currentUser = FirebaseManager.getCurrentUser()
                if (currentUser != null) {
                    val serversResult = FirebaseManager.getUserServers(currentUser.uid)
                    serversResult.onSuccess { servers ->
                        _availableServers.value = servers
                    }.onFailure { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load servers"
                    }
                } else {
                    _errorMessage.value = "User not logged in"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load servers"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun connect(server: VpnServer) {
        viewModelScope.launch {
            try {
                _connectionStatus.value = _connectionStatus.value?.copy(
                    isConnecting = true,
                    serverName = server.name
                )
                
                // Simulate connection process
                delay(2000)
                
                // Start session tracking
                sessionStartTime = Date()
                currentSession = UsageRecord(
                    userId = FirebaseManager.getCurrentUser()?.uid ?: "",
                    serverId = server.id,
                    sessionId = UUID.randomUUID().toString(),
                    connectionStartTime = sessionStartTime,
                    deviceInfo = android.os.Build.MODEL,
                    appVersion = "1.0.0"
                )
                
                _connectionStatus.value = ConnectionStatus(
                    isConnected = true,
                    isConnecting = false,
                    serverName = server.name,
                    serverId = server.id
                )
                
                _successMessage.value = "Connected to ${server.name}"
                
                // Start stats monitoring
                startStatsMonitoring()
                
            } catch (e: Exception) {
                _connectionStatus.value = _connectionStatus.value?.copy(isConnecting = false)
                _errorMessage.value = e.message ?: "Connection failed"
            }
        }
    }

    fun disconnect() {
        viewModelScope.launch {
            try {
                _connectionStatus.value = _connectionStatus.value?.copy(isDisconnecting = true)
                
                // Stop stats monitoring
                stopStatsMonitoring()
                
                // Save session data
                currentSession?.let { session ->
                    val endTime = Date()
                    val duration = (endTime.time - (sessionStartTime?.time ?: endTime.time)) / 1000
                    
                    val finalSession = session.copy(
                        connectionEndTime = endTime,
                        durationSeconds = duration,
                        bytesUploaded = (Math.random() * 1024 * 1024 * 10).toLong(), // Simulated
                        bytesDownloaded = (Math.random() * 1024 * 1024 * 50).toLong() // Simulated
                    )
                    
                    FirebaseManager.recordUsage(finalSession)
                }
                
                // Simulate disconnection process
                delay(1000)
                
                _connectionStatus.value = ConnectionStatus()
                _connectionStats.value = ConnectionStats()
                _successMessage.value = "Disconnected"
                
                // Reload user data to update usage
                loadUserData()
                
            } catch (e: Exception) {
                _connectionStatus.value = _connectionStatus.value?.copy(isDisconnecting = false)
                _errorMessage.value = e.message ?: "Disconnection failed"
            }
        }
    }

    fun setAutoServerSelection(enabled: Boolean) {
        settingsManager.setAutoServerSelection(enabled)
    }

    private fun startStatsMonitoring() {
        statsUpdateJob = viewModelScope.launch {
            while (_connectionStatus.value?.isConnected == true) {
                updateConnectionStats()
                delay(1000) // Update every second
            }
        }
    }

    private fun stopStatsMonitoring() {
        statsUpdateJob?.cancel()
        statsUpdateJob = null
    }

    private fun updateConnectionStats() {
        val startTime = sessionStartTime ?: return
        val currentTime = Date()
        val durationSeconds = (currentTime.time - startTime.time) / 1000
        
        // Simulate realistic stats
        val uploadSpeed = 50 + (Math.random() * 100) // 50-150 KB/s
        val downloadSpeed = 200 + (Math.random() * 800) // 200-1000 KB/s
        val sessionDataMB = (durationSeconds * (uploadSpeed + downloadSpeed)) / 1024.0 / 60.0
        
        val hours = durationSeconds / 3600
        val minutes = (durationSeconds % 3600) / 60
        val seconds = durationSeconds % 60
        val formattedDuration = String.format("%02d:%02d:%02d", hours, minutes, seconds)
        
        _connectionStats.value = ConnectionStats(
            uploadSpeed = uploadSpeed,
            downloadSpeed = downloadSpeed,
            sessionDataMB = sessionDataMB,
            formattedDuration = formattedDuration
        )
    }

    override fun onCleared() {
        super.onCleared()
        stopStatsMonitoring()
    }
}
