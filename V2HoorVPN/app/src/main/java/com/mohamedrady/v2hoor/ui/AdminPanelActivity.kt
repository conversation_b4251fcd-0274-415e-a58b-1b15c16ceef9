package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminPanelBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.viewmodel.AdminPanelViewModel
import kotlinx.coroutines.launch

class AdminPanelActivity : BaseActivity() {
    
    private lateinit var binding: ActivityAdminPanelBinding
    private val viewModel: AdminPanelViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAdminPanelBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupUI()
        observeViewModel()
        
        // Check if user is admin
        viewModel.checkAdminAccess()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.admin_panel)
        }
    }

    private fun setupUI() {
        binding.apply {
            cardUserManagement.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, UserManagementActivity::class.java))
            }

            cardServerManagement.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, AdminServerManagementActivity::class.java))
            }

            cardUsageStatistics.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, UsageStatisticsActivity::class.java))
            }

            cardSystemSettings.setOnClickListener {
                startActivity(Intent(this@AdminPanelActivity, SystemSettingsActivity::class.java))
            }
        }
    }

    private fun observeViewModel() {
        viewModel.isAdmin.observe(this) { isAdmin ->
            if (!isAdmin) {
                toastError(R.string.access_denied)
                finish()
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            // Show/hide loading indicator if needed
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
