package com.mohamedrady.v2hoor.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.util.SettingsManager
import kotlinx.coroutines.launch

class AdvancedSettingsViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsManager = SettingsManager(application)

    private val _settings = MutableLiveData<AdvancedSettings>()
    val settings: LiveData<AdvancedSettings> = _settings

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage

    data class AdvancedSettings(
        val autoConnect: Boolean = false,
        val killSwitch: Boolean = true,
        val customDns: Boolean = false,
        val primaryDns: String = "*******",
        val secondaryDns: String = "*******",
        val connectionTimeout: Int = 30,
        val preferredProtocol: String = "Auto",
        val notificationsEnabled: Boolean = true,
        val quotaAlertsEnabled: Boolean = true,
        val subscriptionAlertsEnabled: Boolean = true,
        val dataSavingMode: Boolean = false,
        val backgroundConnection: Boolean = true
    )

    fun loadSettings() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val currentSettings = AdvancedSettings(
                    autoConnect = settingsManager.getAutoConnect(),
                    killSwitch = settingsManager.getKillSwitch(),
                    customDns = settingsManager.getCustomDns(),
                    primaryDns = settingsManager.getPrimaryDns(),
                    secondaryDns = settingsManager.getSecondaryDns(),
                    connectionTimeout = settingsManager.getConnectionTimeout(),
                    preferredProtocol = settingsManager.getPreferredProtocol(),
                    notificationsEnabled = settingsManager.getNotificationsEnabled(),
                    quotaAlertsEnabled = settingsManager.getQuotaAlertsEnabled(),
                    subscriptionAlertsEnabled = settingsManager.getSubscriptionAlertsEnabled(),
                    dataSavingMode = settingsManager.getDataSavingMode(),
                    backgroundConnection = settingsManager.getBackgroundConnection()
                )
                
                _settings.value = currentSettings
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load settings"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun setAutoConnect(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setAutoConnect(enabled)
                _successMessage.value = "Auto-connect setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update auto-connect setting"
            }
        }
    }

    fun setKillSwitch(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setKillSwitch(enabled)
                _successMessage.value = "Kill switch setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update kill switch setting"
            }
        }
    }

    fun setCustomDns(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setCustomDns(enabled)
                _successMessage.value = "Custom DNS setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update custom DNS setting"
            }
        }
    }

    fun saveDnsSettings(primaryDns: String, secondaryDns: String) {
        viewModelScope.launch {
            try {
                if (!isValidIpAddress(primaryDns)) {
                    _errorMessage.value = "Invalid primary DNS address"
                    return@launch
                }
                
                if (secondaryDns.isNotEmpty() && !isValidIpAddress(secondaryDns)) {
                    _errorMessage.value = "Invalid secondary DNS address"
                    return@launch
                }
                
                settingsManager.setPrimaryDns(primaryDns)
                settingsManager.setSecondaryDns(secondaryDns)
                _successMessage.value = "DNS settings saved successfully"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to save DNS settings"
            }
        }
    }

    fun setConnectionTimeout(timeout: Int) {
        viewModelScope.launch {
            try {
                settingsManager.setConnectionTimeout(timeout)
                _successMessage.value = "Connection timeout updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update connection timeout"
            }
        }
    }

    fun setPreferredProtocol(protocol: String) {
        viewModelScope.launch {
            try {
                settingsManager.setPreferredProtocol(protocol)
                _successMessage.value = "Preferred protocol updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update preferred protocol"
            }
        }
    }

    fun setNotificationsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setNotificationsEnabled(enabled)
                _successMessage.value = "Notifications setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update notifications setting"
            }
        }
    }

    fun setQuotaAlertsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setQuotaAlertsEnabled(enabled)
                _successMessage.value = "Quota alerts setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update quota alerts setting"
            }
        }
    }

    fun setSubscriptionAlertsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setSubscriptionAlertsEnabled(enabled)
                _successMessage.value = "Subscription alerts setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update subscription alerts setting"
            }
        }
    }

    fun setDataSavingMode(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setDataSavingMode(enabled)
                _successMessage.value = "Data saving mode updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update data saving mode"
            }
        }
    }

    fun setBackgroundConnection(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsManager.setBackgroundConnection(enabled)
                _successMessage.value = "Background connection setting updated"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update background connection setting"
            }
        }
    }

    fun resetAdvancedSettings() {
        viewModelScope.launch {
            try {
                // Reset all advanced settings to defaults
                settingsManager.setAutoConnect(false)
                settingsManager.setKillSwitch(true)
                settingsManager.setCustomDns(false)
                settingsManager.setPrimaryDns("*******")
                settingsManager.setSecondaryDns("*******")
                settingsManager.setConnectionTimeout(30)
                settingsManager.setPreferredProtocol("Auto")
                settingsManager.setNotificationsEnabled(true)
                settingsManager.setQuotaAlertsEnabled(true)
                settingsManager.setSubscriptionAlertsEnabled(true)
                settingsManager.setDataSavingMode(false)
                settingsManager.setBackgroundConnection(true)
                
                _successMessage.value = "Advanced settings reset to defaults"
                loadSettings()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to reset advanced settings"
            }
        }
    }

    private fun isValidIpAddress(ip: String): Boolean {
        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false
            
            parts.forEach { part ->
                val num = part.toInt()
                if (num < 0 || num > 255) return false
            }
            true
        } catch (e: Exception) {
            false
        }
    }
}
