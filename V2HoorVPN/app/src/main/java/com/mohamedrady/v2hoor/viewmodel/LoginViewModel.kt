package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseUser
import com.mohamedrady.v2hoor.dto.User
import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.launch

class LoginViewModel : ViewModel() {

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _loginResult = MutableLiveData<Result<FirebaseUser>>()
    val loginResult: LiveData<Result<FirebaseUser>> = _loginResult

    private val _userStatus = MutableLiveData<UserStatus>()
    val userStatus: LiveData<UserStatus> = _userStatus

    sealed class UserStatus {
        object Valid : UserStatus()
        object SubscriptionExpired : UserStatus()
        object DataQuotaExceeded : UserStatus()
        object AccountInactive : UserStatus()
    }

    fun isUserLoggedIn(): Boolean {
        return FirebaseManager.isUserLoggedIn()
    }

    fun signIn(email: String, password: String) {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.signIn(email, password)
                
                result.onSuccess { firebaseUser ->
                    // Check user status in Firestore
                    checkUserStatus(firebaseUser.uid)
                }.onFailure { exception ->
                    _loginResult.value = Result.failure(exception)
                    _isLoading.value = false
                }
                
            } catch (e: Exception) {
                _loginResult.value = Result.failure(e)
                _isLoading.value = false
            }
        }
    }

    private suspend fun checkUserStatus(userId: String) {
        try {
            val userResult = FirebaseManager.getUserData(userId)
            
            userResult.onSuccess { user ->
                val status = when {
                    !user.isActive -> UserStatus.AccountInactive
                    user.isSubscriptionExpired() -> UserStatus.SubscriptionExpired
                    user.isDataQuotaExceeded() -> UserStatus.DataQuotaExceeded
                    else -> UserStatus.Valid
                }
                
                _userStatus.value = status
                
                if (status == UserStatus.Valid) {
                    _loginResult.value = Result.success(FirebaseManager.getCurrentUser()!!)
                }
                
            }.onFailure { exception ->
                _loginResult.value = Result.failure(exception)
            }
            
        } catch (e: Exception) {
            _loginResult.value = Result.failure(e)
        } finally {
            _isLoading.value = false
        }
    }

    fun signOut() {
        FirebaseManager.signOut()
    }
}
