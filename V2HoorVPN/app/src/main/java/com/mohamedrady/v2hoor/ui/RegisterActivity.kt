package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityRegisterBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.RegisterViewModel
import kotlinx.coroutines.launch

class RegisterActivity : BaseActivity() {
    
    private lateinit var binding: ActivityRegisterBinding
    private val viewModel: RegisterViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRegisterBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        binding.apply {
            btnRegister.setOnClickListener {
                val username = etUsername.text.toString().trim()
                val email = etEmail.text.toString().trim()
                val password = etPassword.text.toString().trim()
                val confirmPassword = etConfirmPassword.text.toString().trim()
                
                if (validateInput(username, email, password, confirmPassword)) {
                    viewModel.createUser(email, password, username)
                }
            }

            btnBackToLogin.setOnClickListener {
                finish()
            }
        }
    }

    private fun observeViewModel() {
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnRegister.isEnabled = !isLoading
            binding.btnBackToLogin.isEnabled = !isLoading
        }

        viewModel.registerResult.observe(this) { result ->
            result.onSuccess { user ->
                toastSuccess(getString(R.string.registration_success))
                navigateToMain()
            }.onFailure { exception ->
                toastError(exception.message ?: getString(R.string.registration_failed))
            }
        }
    }

    private fun validateInput(username: String, email: String, password: String, confirmPassword: String): Boolean {
        return when {
            username.isEmpty() -> {
                binding.etUsername.error = getString(R.string.username_required)
                false
            }
            username.length < 3 -> {
                binding.etUsername.error = getString(R.string.username_too_short)
                false
            }
            email.isEmpty() -> {
                binding.etEmail.error = getString(R.string.email_required)
                false
            }
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> {
                binding.etEmail.error = getString(R.string.invalid_email)
                false
            }
            password.isEmpty() -> {
                binding.etPassword.error = getString(R.string.password_required)
                false
            }
            password.length < 6 -> {
                binding.etPassword.error = getString(R.string.password_too_short)
                false
            }
            confirmPassword.isEmpty() -> {
                binding.etConfirmPassword.error = getString(R.string.confirm_password_required)
                false
            }
            password != confirmPassword -> {
                binding.etConfirmPassword.error = getString(R.string.passwords_do_not_match)
                false
            }
            else -> true
        }
    }

    private fun navigateToMain() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}
