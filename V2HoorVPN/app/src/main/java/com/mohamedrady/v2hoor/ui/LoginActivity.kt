package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityLoginBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.LoginViewModel
import kotlinx.coroutines.launch

class LoginActivity : BaseActivity() {
    
    private lateinit var binding: ActivityLoginBinding
    private val viewModel: LoginViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        observeViewModel()
        
        // Check if user is already logged in
        if (viewModel.isUserLoggedIn()) {
            navigateToMain()
        }
    }

    private fun setupUI() {
        binding.apply {
            btnLogin.setOnClickListener {
                val email = etEmail.text.toString().trim()
                val password = etPassword.text.toString().trim()
                
                if (validateInput(email, password)) {
                    viewModel.signIn(email, password)
                }
            }

            btnRegister.setOnClickListener {
                startActivity(Intent(this@LoginActivity, RegisterActivity::class.java))
            }

            tvForgotPassword.setOnClickListener {
                // TODO: Implement forgot password functionality
                toast(R.string.feature_coming_soon)
            }
        }
    }

    private fun observeViewModel() {
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnLogin.isEnabled = !isLoading
            binding.btnRegister.isEnabled = !isLoading
        }

        viewModel.loginResult.observe(this) { result ->
            result.onSuccess { user ->
                toastSuccess(getString(R.string.login_success, user.email))
                navigateToMain()
            }.onFailure { exception ->
                toastError(exception.message ?: getString(R.string.login_failed))
            }
        }

        viewModel.userStatus.observe(this) { status ->
            when (status) {
                is LoginViewModel.UserStatus.SubscriptionExpired -> {
                    toastError(R.string.subscription_expired)
                }
                is LoginViewModel.UserStatus.DataQuotaExceeded -> {
                    toastError(R.string.data_quota_exceeded)
                }
                is LoginViewModel.UserStatus.AccountInactive -> {
                    toastError(R.string.account_inactive)
                }
                is LoginViewModel.UserStatus.Valid -> {
                    // User is valid, proceed to main activity
                }
            }
        }
    }

    private fun validateInput(email: String, password: String): Boolean {
        return when {
            email.isEmpty() -> {
                binding.etEmail.error = getString(R.string.email_required)
                false
            }
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> {
                binding.etEmail.error = getString(R.string.invalid_email)
                false
            }
            password.isEmpty() -> {
                binding.etPassword.error = getString(R.string.password_required)
                false
            }
            password.length < 6 -> {
                binding.etPassword.error = getString(R.string.password_too_short)
                false
            }
            else -> true
        }
    }

    private fun navigateToMain() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}
