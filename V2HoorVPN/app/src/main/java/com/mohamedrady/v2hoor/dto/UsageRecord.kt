package com.mohamedrady.v2hoor.dto

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp
import java.util.Date

/**
 * Usage tracking data model for Firebase Firestore
 */
data class UsageRecord(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val serverId: String = "",
    val sessionId: String = "", // Unique session identifier
    val bytesUploaded: Long = 0L,
    val bytesDownloaded: Long = 0L,
    val connectionStartTime: Date? = null,
    val connectionEndTime: Date? = null,
    val durationSeconds: Long = 0L,
    val deviceInfo: String = "", // Device model/info
    val appVersion: String = "",
    @ServerTimestamp
    val createdAt: Date? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        userId = "",
        serverId = "",
        sessionId = "",
        bytesUploaded = 0L,
        bytesDownloaded = 0L,
        connectionStartTime = null,
        connectionEndTime = null,
        durationSeconds = 0L,
        deviceInfo = "",
        appVersion = "",
        createdAt = null
    )

    /**
     * Get total bytes transferred
     */
    fun getTotalBytes(): Long {
        return bytesUploaded + bytesDownloaded
    }

    /**
     * Get total data in MB
     */
    fun getTotalDataMB(): Double {
        return getTotalBytes() / (1024.0 * 1024.0)
    }

    /**
     * Get total data in GB
     */
    fun getTotalDataGB(): Double {
        return getTotalBytes() / (1024.0 * 1024.0 * 1024.0)
    }

    /**
     * Check if session is currently active
     */
    fun isActiveSession(): Boolean {
        return connectionStartTime != null && connectionEndTime == null
    }

    /**
     * Get formatted duration string
     */
    fun getFormattedDuration(): String {
        val hours = durationSeconds / 3600
        val minutes = (durationSeconds % 3600) / 60
        val seconds = durationSeconds % 60
        
        return when {
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
            minutes > 0 -> String.format("%02d:%02d", minutes, seconds)
            else -> "${seconds}s"
        }
    }
}
