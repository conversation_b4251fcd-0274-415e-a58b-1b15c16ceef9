package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope

import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.launch

class RegisterViewModel : ViewModel() {

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _registerResult = MutableLiveData<Result<FirebaseManager.MockFirebaseUser>>()
    val registerResult: LiveData<Result<FirebaseManager.MockFirebaseUser>> = _registerResult

    fun createUser(email: String, password: String, username: String) {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.createUser(email, password, username)
                _registerResult.value = result
            } catch (e: Exception) {
                _registerResult.value = Result.failure(e)
            } finally {
                _isLoading.value = false
            }
        }
    }
}
