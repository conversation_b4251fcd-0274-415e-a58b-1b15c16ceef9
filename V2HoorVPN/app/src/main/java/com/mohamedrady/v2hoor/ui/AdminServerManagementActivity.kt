package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminServerManagementBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.viewmodel.AdminServerManagementViewModel

class AdminServerManagementActivity : BaseActivity() {
    
    private lateinit var binding: ActivityAdminServerManagementBinding
    private val viewModel: AdminServerManagementViewModel by viewModels()
    private lateinit var adapter: AdminServerAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAdminServerManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupUI()
        observeViewModel()
        
        // Load servers
        viewModel.loadServers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.server_management)
        }
    }

    private fun setupRecyclerView() {
        adapter = AdminServerAdapter(
            onServerClick = { server ->
                // Handle server item click (edit server)
                viewModel.selectServer(server)
            },
            onToggleActive = { server ->
                // Toggle server active status
                viewModel.toggleServerStatus(server)
            }
        )
        
        binding.recyclerViewServers.apply {
            layoutManager = LinearLayoutManager(this@AdminServerManagementActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupUI() {
        binding.apply {
            fabAddServer.setOnClickListener {
                // TODO: Open add server dialog
                toast(R.string.feature_coming_soon)
            }

            swipeRefreshLayout.setOnRefreshListener {
                viewModel.loadServers()
            }
        }
    }

    private fun observeViewModel() {
        viewModel.servers.observe(this) { servers ->
            adapter.submitList(servers)
            binding.textViewEmpty.visibility = if (servers.isEmpty()) View.VISIBLE else View.GONE
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.swipeRefreshLayout.isRefreshing = isLoading
            binding.progressBar.visibility = if (isLoading && adapter.itemCount == 0) View.VISIBLE else View.GONE
        }

        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let {
                toastError(it)
            }
        }

        viewModel.successMessage.observe(this) { successMessage ->
            successMessage?.let {
                toastSuccess(it)
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
