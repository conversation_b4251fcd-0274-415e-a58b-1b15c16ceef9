package com.mohamedrady.v2hoor.handler

import android.util.Log
import com.mohamedrady.v2hoor.dto.User
import com.mohamedrady.v2hoor.dto.UsageRecord
import com.mohamedrady.v2hoor.dto.VpnServer
import java.util.Date

/**
 * Mock Firebase Manager for testing without Firebase dependencies
 */
object FirebaseManager {
    // Mock Firebase User class
    data class MockFirebaseUser(
        val uid: String,
        val email: String?
    )

    // Mock current user
    private var mockUser: MockFirebaseUser? = null

    /**
     * Get current user
     */
    fun getCurrentUser(): MockFirebaseUser? = mockUser

    /**
     * Check if user is logged in
     */
    fun isUserLoggedIn(): Boolean = mockUser != null

    /**
     * Sign in with email and password
     */
    suspend fun signIn(email: String, password: String): Result<MockFirebaseUser> {
        return try {
            // Mock successful login
            mockUser = MockFirebaseUser("mock_uid_123", email)
            Result.success(mockUser!!)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Create user with email and password
     */
    suspend fun createUser(email: String, password: String, username: String): Result<MockFirebaseUser> {
        return try {
            // Mock successful registration
            mockUser = MockFirebaseUser("mock_uid_123", email)
            Result.success(mockUser!!)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sign out current user
     */
    fun signOut() {
        mockUser = null
    }

    /**
     * Get user data from Firestore
     */
    suspend fun getUserData(userId: String): Result<User> {
        return try {
            // Mock user data
            val user = User(
                id = userId,
                email = "<EMAIL>",
                username = "Test User",
                isAdmin = false,
                dataQuotaGB = 10.0,
                dataUsedGB = 2.5,
                subscriptionExpiryDate = Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000), // 30 days from now
                isActive = true,
                allowedServerIds = listOf("server1", "server2"),
                createdAt = Date(),
                lastLoginAt = Date()
            )
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    /**
     * Get all servers
     */
    suspend fun getAllServers(): Result<List<VpnServer>> {
        return try {
            // Mock server data
            val servers = listOf(
                VpnServer(
                    id = "server1",
                    name = "US Server 1",
                    country = "United States",
                    city = "New York",
                    serverType = "vmess",
                    address = "us1.example.com",
                    port = 443,
                    uuid = "12345678-1234-1234-1234-123456789abc",
                    alterId = 0,
                    security = "auto",
                    network = "ws",
                    path = "/path",
                    host = "us1.example.com",
                    tls = "tls",
                    isActive = true,
                    priority = 1,
                    maxUsers = 100,
                    currentUsers = 25,
                    createdAt = Date(),
                    updatedAt = Date()
                )
            )
            Result.success(servers)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get servers for specific user
     */
    suspend fun getUserServers(userId: String): Result<List<VpnServer>> {
        return getAllServers() // For mock, return all servers
    }

    /**
     * Save usage record
     */
    suspend fun saveUsageRecord(usageRecord: UsageRecord): Result<Unit> {
        return try {
            // Mock save operation
            Log.d("FirebaseManager", "Mock: Saved usage record for user ${usageRecord.userId}")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update user data usage
     */
    suspend fun updateUserDataUsage(userId: String, dataUsedGB: Double): Result<Unit> {
        return try {
            // Mock update operation
            Log.d("FirebaseManager", "Mock: Updated data usage for user $userId: $dataUsedGB GB")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update user last login
     */
    suspend fun updateUserLastLogin(userId: String): Result<Unit> {
        return try {
            // Mock update operation
            Log.d("FirebaseManager", "Mock: Updated last login for user $userId")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get all users (admin only)
     */
    suspend fun getAllUsers(): Result<List<User>> {
        return try {
            // Mock users data
            val users = listOf(
                User(
                    id = "user1",
                    email = "<EMAIL>",
                    username = "User 1",
                    isAdmin = false,
                    dataQuotaGB = 10.0,
                    dataUsedGB = 5.2,
                    subscriptionExpiryDate = Date(System.currentTimeMillis() + 15L * 24 * 60 * 60 * 1000),
                    isActive = true,
                    allowedServerIds = listOf("server1"),
                    createdAt = Date(),
                    lastLoginAt = Date()
                )
            )
            Result.success(users)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update user (admin only)
     */
    suspend fun updateUser(user: User): Result<Unit> {
        return try {
            // Mock update operation
            Log.d("FirebaseManager", "Mock: Updated user ${user.id}")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Add server (admin only)
     */
    suspend fun addServer(server: VpnServer): Result<String> {
        return try {
            // Mock add operation
            val serverId = "mock_server_${System.currentTimeMillis()}"
            Log.d("FirebaseManager", "Mock: Added server $serverId")
            Result.success(serverId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update server (admin only)
     */
    suspend fun updateServer(server: VpnServer): Result<Unit> {
        return try {
            // Mock update operation
            Log.d("FirebaseManager", "Mock: Updated server ${server.id}")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete server (admin only)
     */
    suspend fun deleteServer(serverId: String): Result<Unit> {
        return try {
            // Mock delete operation
            Log.d("FirebaseManager", "Mock: Deleted server $serverId")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save server (admin only)
     */
    suspend fun saveServer(server: VpnServer): Result<Unit> {
        return try {
            // Mock save operation
            Log.d("FirebaseManager", "Mock: Saved server ${server.id}")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Record usage data
     */
    suspend fun recordUsage(usageRecord: UsageRecord): Result<Unit> {
        return try {
            // Mock record operation
            Log.d("FirebaseManager", "Mock: Recorded usage for user ${usageRecord.userId}")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get all usage records (admin only)
     */
    suspend fun getAllUsageRecords(): Result<List<UsageRecord>> {
        return try {
            // Mock usage records
            val records = listOf(
                UsageRecord(
                    id = "usage1",
                    userId = "user1",
                    serverId = "server1",
                    sessionId = "session1",
                    bytesUploaded = 1024L * 1024L * 50L, // 50MB
                    bytesDownloaded = 1024L * 1024L * 200L, // 200MB
                    connectionStartTime = Date(System.currentTimeMillis() - 3600000), // 1 hour ago
                    connectionEndTime = Date(),
                    durationSeconds = 3600L,
                    deviceInfo = "Android 12",
                    appVersion = "1.0.0",
                    createdAt = Date()
                )
            )
            Result.success(records)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Clear all usage data (admin only)
     */
    suspend fun clearAllUsageData(): Result<Unit> {
        return try {
            // Mock clear operation
            Log.d("FirebaseManager", "Mock: Cleared all usage data")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete user (admin only)
     */
    suspend fun deleteUser(userId: String): Result<Unit> {
        return try {
            // Mock delete operation
            Log.d("FirebaseManager", "Mock: Deleted user $userId")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

}
