package com.mohamedrady.v2hoor.handler

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.dto.User
import com.mohamedrady.v2hoor.dto.UsageRecord
import com.mohamedrady.v2hoor.dto.VpnServer
import kotlinx.coroutines.tasks.await
import java.util.Date

/**
 * Firebase Manager for handling authentication and Firestore operations
 */
object FirebaseManager {
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()

    // Collection names
    private const val USERS_COLLECTION = "users"
    private const val SERVERS_COLLECTION = "servers"
    private const val USAGE_COLLECTION = "usage_records"

    /**
     * Get current Firebase user
     */
    fun getCurrentUser(): FirebaseUser? = auth.currentUser

    /**
     * Check if user is logged in
     */
    fun isUserLoggedIn(): Boolean = getCurrentUser() != null

    /**
     * Sign in with email and password
     */
    suspend fun signIn(email: String, password: String): Result<FirebaseUser> {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            result.user?.let { user ->
                // Update last login time
                updateUserLastLogin(user.uid)
                Result.success(user)
            } ?: Result.failure(Exception("Sign in failed"))
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Sign in failed", e)
            Result.failure(e)
        }
    }

    /**
     * Create new user account
     */
    suspend fun createUser(email: String, password: String, username: String): Result<FirebaseUser> {
        return try {
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            result.user?.let { firebaseUser ->
                // Create user document in Firestore
                val user = User(
                    id = firebaseUser.uid,
                    username = username,
                    email = email,
                    isAdmin = false,
                    isActive = true,
                    dataQuotaGB = 10.0, // Default 10GB quota
                    usedDataGB = 0.0,
                    subscriptionExpiry = null,
                    allowedServers = emptyList(),
                    lastLoginAt = Date()
                )
                createUserDocument(user)
                Result.success(firebaseUser)
            } ?: Result.failure(Exception("User creation failed"))
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "User creation failed", e)
            Result.failure(e)
        }
    }

    /**
     * Sign out current user
     */
    fun signOut() {
        auth.signOut()
    }

    /**
     * Create user document in Firestore
     */
    private suspend fun createUserDocument(user: User) {
        try {
            firestore.collection(USERS_COLLECTION)
                .document(user.id)
                .set(user)
                .await()
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to create user document", e)
            throw e
        }
    }

    /**
     * Get user data from Firestore
     */
    suspend fun getUserData(userId: String): Result<User> {
        return try {
            val document = firestore.collection(USERS_COLLECTION)
                .document(userId)
                .get()
                .await()
            
            val user = document.toObject(User::class.java)
            user?.let { Result.success(it) } ?: Result.failure(Exception("User not found"))
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to get user data", e)
            Result.failure(e)
        }
    }

    /**
     * Update user's last login time
     */
    private suspend fun updateUserLastLogin(userId: String) {
        try {
            firestore.collection(USERS_COLLECTION)
                .document(userId)
                .update("lastLoginAt", Date())
                .await()
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to update last login", e)
        }
    }

    /**
     * Get all active servers
     */
    suspend fun getActiveServers(): Result<List<VpnServer>> {
        return try {
            val querySnapshot = firestore.collection(SERVERS_COLLECTION)
                .whereEqualTo("active", true)
                .orderBy("priority", Query.Direction.DESCENDING)
                .get()
                .await()
            
            val servers = querySnapshot.toObjects(VpnServer::class.java)
            Result.success(servers)
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to get servers", e)
            Result.failure(e)
        }
    }

    /**
     * Get servers accessible by a specific user
     */
    suspend fun getUserServers(userId: String): Result<List<VpnServer>> {
        return try {
            val querySnapshot = firestore.collection(SERVERS_COLLECTION)
                .whereEqualTo("active", true)
                .get()
                .await()
            
            val allServers = querySnapshot.toObjects(VpnServer::class.java)
            val userServers = allServers.filter { server ->
                server.canUserAccess(userId)
            }.sortedByDescending { it.priority }
            
            Result.success(userServers)
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to get user servers", e)
            Result.failure(e)
        }
    }

    /**
     * Record usage data
     */
    suspend fun recordUsage(usageRecord: UsageRecord): Result<Unit> {
        return try {
            firestore.collection(USAGE_COLLECTION)
                .add(usageRecord)
                .await()
            
            // Update user's total usage
            updateUserDataUsage(usageRecord.userId, usageRecord.getTotalDataGB())
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to record usage", e)
            Result.failure(e)
        }
    }

    /**
     * Update user's data usage
     */
    private suspend fun updateUserDataUsage(userId: String, additionalDataGB: Double) {
        try {
            val userDoc = firestore.collection(USERS_COLLECTION).document(userId)
            firestore.runTransaction { transaction ->
                val snapshot = transaction.get(userDoc)
                val currentUsage = snapshot.getDouble("usedDataGB") ?: 0.0
                transaction.update(userDoc, "usedDataGB", currentUsage + additionalDataGB)
            }.await()
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to update user data usage", e)
        }
    }

    /**
     * Get user's usage history
     */
    suspend fun getUserUsageHistory(userId: String, limit: Int = 50): Result<List<UsageRecord>> {
        return try {
            val querySnapshot = firestore.collection(USAGE_COLLECTION)
                .whereEqualTo("userId", userId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            val usageRecords = querySnapshot.toObjects(UsageRecord::class.java)
            Result.success(usageRecords)
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to get usage history", e)
            Result.failure(e)
        }
    }
}
