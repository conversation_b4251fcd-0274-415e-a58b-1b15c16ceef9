package com.mohamedrady.v2hoor.util

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.ui.MainActivity

/**
 * Notification Manager for V2Hoor VPN
 */
class V2HoorNotificationManager(private val context: Context) {
    
    private val notificationManager = NotificationManagerCompat.from(context)
    
    companion object {
        private const val CHANNEL_VPN_STATUS = "vpn_status"
        private const val CHANNEL_ALERTS = "alerts"
        private const val CHANNEL_USAGE = "usage"
        
        private const val NOTIFICATION_VPN_STATUS = 1001
        private const val NOTIFICATION_QUOTA_WARNING = 1002
        private const val NOTIFICATION_QUOTA_EXCEEDED = 1003
        private const val NOTIFICATION_SUBSCRIPTION_EXPIRING = 1004
        private const val NOTIFICATION_SUBSCRIPTION_EXPIRED = 1005
    }
    
    init {
        createNotificationChannels()
    }
    
    /**
     * Create notification channels for Android O+
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_VPN_STATUS,
                    context.getString(R.string.channel_vpn_status),
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = context.getString(R.string.channel_vpn_status_desc)
                    setShowBadge(false)
                },
                
                NotificationChannel(
                    CHANNEL_ALERTS,
                    context.getString(R.string.channel_alerts),
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = context.getString(R.string.channel_alerts_desc)
                    enableVibration(true)
                },
                
                NotificationChannel(
                    CHANNEL_USAGE,
                    context.getString(R.string.channel_usage),
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = context.getString(R.string.channel_usage_desc)
                }
            )
            
            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { channel ->
                systemNotificationManager.createNotificationChannel(channel)
            }
        }
    }
    
    /**
     * Show VPN connection status notification
     */
    fun showVpnStatusNotification(
        isConnected: Boolean,
        serverName: String = "",
        uploadSpeed: String = "",
        downloadSpeed: String = ""
    ) {
        val title = if (isConnected) {
            context.getString(R.string.vpn_connected)
        } else {
            context.getString(R.string.vpn_disconnected)
        }
        
        val content = if (isConnected && serverName.isNotEmpty()) {
            context.getString(R.string.connected_to_server, serverName)
        } else {
            context.getString(R.string.tap_to_connect)
        }
        
        val bigText = if (isConnected && uploadSpeed.isNotEmpty() && downloadSpeed.isNotEmpty()) {
            context.getString(R.string.connection_stats_notification, uploadSpeed, downloadSpeed)
        } else {
            content
        }
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_VPN_STATUS)
            .setSmallIcon(R.drawable.ic_vpn_24dp)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(NotificationCompat.BigTextStyle().bigText(bigText))
            .setContentIntent(pendingIntent)
            .setOngoing(isConnected)
            .setAutoCancel(!isConnected)
            .setColor(context.getColor(if (isConnected) R.color.success else R.color.info))
            .build()
        
        notificationManager.notify(NOTIFICATION_VPN_STATUS, notification)
    }
    
    /**
     * Hide VPN status notification
     */
    fun hideVpnStatusNotification() {
        notificationManager.cancel(NOTIFICATION_VPN_STATUS)
    }
    
    /**
     * Show data quota warning notification
     */
    fun showQuotaWarningNotification(usedPercentage: Int, remainingGB: Double) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ALERTS)
            .setSmallIcon(R.drawable.ic_warning_24dp)
            .setContentTitle(context.getString(R.string.quota_warning_title))
            .setContentText(context.getString(R.string.quota_warning_message, usedPercentage, remainingGB))
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(context.getString(R.string.quota_warning_detailed, usedPercentage, remainingGB)))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(context.getColor(R.color.warning))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
        
        notificationManager.notify(NOTIFICATION_QUOTA_WARNING, notification)
    }
    
    /**
     * Show data quota exceeded notification
     */
    fun showQuotaExceededNotification() {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ALERTS)
            .setSmallIcon(R.drawable.ic_error_24dp)
            .setContentTitle(context.getString(R.string.quota_exceeded_title))
            .setContentText(context.getString(R.string.quota_exceeded_message))
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(context.getString(R.string.quota_exceeded_detailed)))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(context.getColor(R.color.error))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
        
        notificationManager.notify(NOTIFICATION_QUOTA_EXCEEDED, notification)
    }
    
    /**
     * Show subscription expiring notification
     */
    fun showSubscriptionExpiringNotification(daysRemaining: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ALERTS)
            .setSmallIcon(R.drawable.ic_schedule_24dp)
            .setContentTitle(context.getString(R.string.subscription_expiring_title))
            .setContentText(context.getString(R.string.subscription_expiring_message, daysRemaining))
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(context.getString(R.string.subscription_expiring_detailed, daysRemaining)))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(context.getColor(R.color.warning))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
        
        notificationManager.notify(NOTIFICATION_SUBSCRIPTION_EXPIRING, notification)
    }
    
    /**
     * Show subscription expired notification
     */
    fun showSubscriptionExpiredNotification() {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ALERTS)
            .setSmallIcon(R.drawable.ic_error_24dp)
            .setContentTitle(context.getString(R.string.subscription_expired_title))
            .setContentText(context.getString(R.string.subscription_expired_message))
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(context.getString(R.string.subscription_expired_detailed)))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(context.getColor(R.color.error))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
        
        notificationManager.notify(NOTIFICATION_SUBSCRIPTION_EXPIRED, notification)
    }
    
    /**
     * Clear all alert notifications
     */
    fun clearAlertNotifications() {
        notificationManager.cancel(NOTIFICATION_QUOTA_WARNING)
        notificationManager.cancel(NOTIFICATION_QUOTA_EXCEEDED)
        notificationManager.cancel(NOTIFICATION_SUBSCRIPTION_EXPIRING)
        notificationManager.cancel(NOTIFICATION_SUBSCRIPTION_EXPIRED)
    }
    
    /**
     * Check if notifications are enabled
     */
    fun areNotificationsEnabled(): Boolean {
        return notificationManager.areNotificationsEnabled()
    }
}
