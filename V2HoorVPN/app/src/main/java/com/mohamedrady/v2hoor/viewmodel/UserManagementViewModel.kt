package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.User
import com.mohamedrady.v2hoor.handler.FirebaseManager
import kotlinx.coroutines.launch

class UserManagementViewModel : ViewModel() {

    private val _users = MutableLiveData<List<User>>()
    val users: LiveData<List<User>> = _users

    private val _selectedUser = MutableLiveData<User?>()
    val selectedUser: LiveData<User?> = _selectedUser

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _successMessage = MutableLiveData<String?>()
    val successMessage: LiveData<String?> = _successMessage

    fun loadUsers() {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.getAllUsers()
                result.onSuccess { usersList ->
                    _users.value = usersList
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load users"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load users"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun selectUser(user: User) {
        _selectedUser.value = user
    }

    fun updateUser(user: User) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.updateUser(user)
                result.onSuccess {
                    _successMessage.value = "User updated successfully"
                    loadUsers() // Refresh the list
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to update user"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update user"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteUser(userId: String) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val result = FirebaseManager.deleteUser(userId)
                result.onSuccess {
                    _successMessage.value = "User deleted successfully"
                    loadUsers() // Refresh the list
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "Failed to delete user"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to delete user"
            } finally {
                _isLoading.value = false
            }
        }
    }
}
