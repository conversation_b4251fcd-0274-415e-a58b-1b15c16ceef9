# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+UseStringDeduplication
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# Enable Gradle daemon for better performance
org.gradle.daemon=true
# Enable configuration cache for faster builds
org.gradle.configuration-cache=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
kotlin.incremental=true
# Kotlin daemon optimizations
kotlin.daemon.jvmargs=-Xmx2048m -XX:+UseG1GC
# Enable Kotlin incremental compilation
kotlin.incremental.useClasspathSnapshot=true
# Kotlin compiler optimizations
kotlin.compiler.execution.strategy=daemon

# TLS and SSL settings to fix connection issues
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.javax.net.ssl.trustStore=NONE
systemProp.javax.net.ssl.trustStoreType=Windows-ROOT

# Disable configuration cache temporarily to avoid serialization issues
org.gradle.configuration-cache=false